.class final Lv2/a1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv2/h4;


# instance fields
.field private final a:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lt2/g1;",
            "Lv2/i4;",
            ">;"
        }
    .end annotation
.end field

.field private final b:Lv2/k1;

.field private c:I

.field private d:Lw2/w;

.field private e:J

.field private final f:Lv2/y0;


# direct methods
.method constructor <init>(Lv2/y0;)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lv2/a1;->a:Ljava/util/Map;

    new-instance v0, Lv2/k1;

    invoke-direct {v0}, Lv2/k1;-><init>()V

    iput-object v0, p0, Lv2/a1;->b:Lv2/k1;

    sget-object v0, Lw2/w;->b:Lw2/w;

    iput-object v0, p0, Lv2/a1;->d:Lw2/w;

    const-wide/16 v0, 0x0

    iput-wide v0, p0, Lv2/a1;->e:J

    iput-object p1, p0, Lv2/a1;->f:Lv2/y0;

    return-void
.end method


# virtual methods
.method public a(I)Lk2/e;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lk2/e<",
            "Lw2/l;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lv2/a1;->b:Lv2/k1;

    invoke-virtual {v0, p1}, Lv2/k1;->d(I)Lk2/e;

    move-result-object p1

    return-object p1
.end method

.method public b()Lw2/w;
    .locals 1

    iget-object v0, p0, Lv2/a1;->d:Lw2/w;

    return-object v0
.end method

.method public c(Lk2/e;I)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk2/e<",
            "Lw2/l;",
            ">;I)V"
        }
    .end annotation

    iget-object v0, p0, Lv2/a1;->b:Lv2/k1;

    invoke-virtual {v0, p1, p2}, Lv2/k1;->b(Lk2/e;I)V

    iget-object p2, p0, Lv2/a1;->f:Lv2/y0;

    invoke-virtual {p2}, Lv2/y0;->f()Lv2/j1;

    move-result-object p2

    invoke-virtual {p1}, Lk2/e;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lw2/l;

    invoke-interface {p2, v0}, Lv2/j1;->a(Lw2/l;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public d(I)V
    .locals 1

    iget-object v0, p0, Lv2/a1;->b:Lv2/k1;

    invoke-virtual {v0, p1}, Lv2/k1;->h(I)Lk2/e;

    return-void
.end method

.method public e(Lt2/g1;)Lv2/i4;
    .locals 1

    iget-object v0, p0, Lv2/a1;->a:Ljava/util/Map;

    invoke-interface {v0, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lv2/i4;

    return-object p1
.end method

.method public f(Lw2/w;)V
    .locals 0

    iput-object p1, p0, Lv2/a1;->d:Lw2/w;

    return-void
.end method

.method public g(Lk2/e;I)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lk2/e<",
            "Lw2/l;",
            ">;I)V"
        }
    .end annotation

    iget-object v0, p0, Lv2/a1;->b:Lv2/k1;

    invoke-virtual {v0, p1, p2}, Lv2/k1;->g(Lk2/e;I)V

    iget-object p2, p0, Lv2/a1;->f:Lv2/y0;

    invoke-virtual {p2}, Lv2/y0;->f()Lv2/j1;

    move-result-object p2

    invoke-virtual {p1}, Lk2/e;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lw2/l;

    invoke-interface {p2, v0}, Lv2/j1;->p(Lw2/l;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public h(Lv2/i4;)V
    .locals 0

    invoke-virtual {p0, p1}, Lv2/a1;->i(Lv2/i4;)V

    return-void
.end method

.method public i(Lv2/i4;)V
    .locals 4

    iget-object v0, p0, Lv2/a1;->a:Ljava/util/Map;

    invoke-virtual {p1}, Lv2/i4;->g()Lt2/g1;

    move-result-object v1

    invoke-interface {v0, v1, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lv2/i4;->h()I

    move-result v0

    iget v1, p0, Lv2/a1;->c:I

    if-le v0, v1, :cond_0

    iput v0, p0, Lv2/a1;->c:I

    :cond_0
    invoke-virtual {p1}, Lv2/i4;->e()J

    move-result-wide v0

    iget-wide v2, p0, Lv2/a1;->e:J

    cmp-long v0, v0, v2

    if-lez v0, :cond_1

    invoke-virtual {p1}, Lv2/i4;->e()J

    move-result-wide v0

    iput-wide v0, p0, Lv2/a1;->e:J

    :cond_1
    return-void
.end method

.method public j()I
    .locals 1

    iget v0, p0, Lv2/a1;->c:I

    return v0
.end method

.method public k(Lw2/l;)Z
    .locals 1

    iget-object v0, p0, Lv2/a1;->b:Lv2/k1;

    invoke-virtual {v0, p1}, Lv2/k1;->c(Lw2/l;)Z

    move-result p1

    return p1
.end method

.method public l(La3/n;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "La3/n<",
            "Lv2/i4;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Lv2/a1;->a:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lv2/i4;

    invoke-interface {p1, v1}, La3/n;->accept(Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method m(Lv2/o;)J
    .locals 5

    iget-object v0, p0, Lv2/a1;->a:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const-wide/16 v1, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lv2/i4;

    invoke-virtual {p1, v3}, Lv2/o;->q(Lv2/i4;)Ly2/c;

    move-result-object v3

    invoke-virtual {v3}, Lcom/google/protobuf/z;->a()I

    move-result v3

    int-to-long v3, v3

    add-long/2addr v1, v3

    goto :goto_0

    :cond_0
    return-wide v1
.end method

.method public n()J
    .locals 2

    iget-wide v0, p0, Lv2/a1;->e:J

    return-wide v0
.end method

.method public o()J
    .locals 2

    iget-object v0, p0, Lv2/a1;->a:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    int-to-long v0, v0

    return-wide v0
.end method

.method p(JLandroid/util/SparseArray;)I
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Landroid/util/SparseArray<",
            "*>;)I"
        }
    .end annotation

    iget-object v0, p0, Lv2/a1;->a:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/util/Map$Entry;

    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lv2/i4;

    invoke-virtual {v3}, Lv2/i4;->h()I

    move-result v3

    invoke-interface {v2}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lv2/i4;

    invoke-virtual {v2}, Lv2/i4;->e()J

    move-result-wide v4

    cmp-long v2, v4, p1

    if-gtz v2, :cond_0

    invoke-virtual {p3, v3}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v2

    if-nez v2, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    invoke-virtual {p0, v3}, Lv2/a1;->d(I)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return v1
.end method

.method public q(Lv2/i4;)V
    .locals 2

    iget-object v0, p0, Lv2/a1;->a:Ljava/util/Map;

    invoke-virtual {p1}, Lv2/i4;->g()Lt2/g1;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lv2/a1;->b:Lv2/k1;

    invoke-virtual {p1}, Lv2/i4;->h()I

    move-result p1

    invoke-virtual {v0, p1}, Lv2/k1;->h(I)Lk2/e;

    return-void
.end method
