name: bonic_bot
description: <PERSON><PERSON><PERSON><PERSON> - AI Voice Assistant with Face Detection

publish_to: 'none'

version: 1.2.0+2

environment:
  sdk: '>=2.18.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI & Animations
  cupertino_icons: ^1.0.2
  lottie: ^2.7.0
  fluttertoast: ^8.2.4

  # Audio & Voice
  audioplayers: ^5.2.1
  speech_to_text: ^6.6.0
  flutter_tts: ^3.8.5

  # Camera & ML
  camera: ^0.10.5+9
  google_mlkit_face_detection: ^0.9.0

  # Wake word detection removed for simplicity

  # Permissions & Device
  permission_handler: ^11.0.1

  # Utilities
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2
  http: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    # Images
    - assets/logo.png
    - assets/splash.png
    - assets/f1.jpeg

    # Lottie Animations
    - assets/lottie/
    - assets/loading.json

    # Audio Files
    - assets/song1.mp3
    - assets/song2.mp3

    # Wake word models removed (using manual activation only)

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Thin.ttf
          weight: 100
        - asset: assets/fonts/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-Black.ttf
          weight: 900
        - asset: assets/fonts/Poppins-Italic.ttf
          style: italic
