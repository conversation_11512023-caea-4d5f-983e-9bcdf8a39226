.class public interface abstract La7/p;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract iterator()La7/g;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "La7/g<",
            "TE;>;"
        }
    .end annotation
.end method
