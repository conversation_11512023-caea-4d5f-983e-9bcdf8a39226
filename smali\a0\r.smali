.class public final synthetic La0/r;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:La0/s;

.field public final synthetic b:Z


# direct methods
.method public synthetic constructor <init>(La0/s;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La0/r;->a:La0/s;

    iput-boolean p2, p0, La0/r;->b:Z

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, La0/r;->a:La0/s;

    iget-boolean v1, p0, La0/r;->b:Z

    invoke-static {v0, v1}, La0/s;->k(La0/s;Z)V

    return-void
.end method
