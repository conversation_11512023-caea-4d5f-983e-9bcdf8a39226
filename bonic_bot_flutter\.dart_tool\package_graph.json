{"roots": ["bonic_bot"], "packages": [{"name": "bonic_bot", "version": "1.2.0+2", "dependencies": ["audioplayers", "camera", "cupertino_icons", "flutter", "flutter_tts", "fluttertoast", "google_mlkit_face_detection", "http", "lottie", "path_provider", "permission_handler", "shared_preferences", "speech_to_text"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "3.0.2", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "google_mlkit_face_detection", "version": "0.9.0", "dependencies": ["flutter", "google_mlkit_commons"]}, {"name": "camera", "version": "0.10.6", "dependencies": ["camera_android", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle"]}, {"name": "flutter_tts", "version": "3.8.5", "dependencies": ["flutter", "flutter_web_plugins"]}, {"name": "speech_to_text", "version": "6.6.0", "dependencies": ["clock", "flutter", "flutter_web_plugins", "js", "json_annotation", "meta", "pedantic", "speech_to_text_macos", "speech_to_text_platform_interface"]}, {"name": "audioplayers", "version": "5.2.1", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_platform_interface", "audioplayers_web", "audioplayers_windows", "file", "flutter", "http", "meta", "path_provider", "synchronized", "uuid"]}, {"name": "fluttertoast", "version": "8.2.12", "dependencies": ["flutter", "flutter_web_plugins", "web"]}, {"name": "lottie", "version": "2.7.0", "dependencies": ["archive", "flutter", "path", "vector_math"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "google_mlkit_commons", "version": "0.6.1", "dependencies": ["flutter"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "camera_web", "version": "0.3.5", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform", "web"]}, {"name": "camera_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "camera_avfoundation", "version": "0.9.19+1", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "camera_android", "version": "0.10.10+3", "dependencies": ["camera_platform_interface", "flutter", "flutter_plugin_android_lifecycle", "stream_transform"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "pedantic", "version": "1.11.1", "dependencies": []}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "speech_to_text_macos", "version": "1.1.0", "dependencies": ["flutter", "plugin_platform_interface", "speech_to_text_platform_interface"]}, {"name": "speech_to_text_platform_interface", "version": "2.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "audioplayers_windows", "version": "3.1.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_web", "version": "4.1.0", "dependencies": ["audioplayers_platform_interface", "flutter", "flutter_web_plugins", "js"]}, {"name": "audioplayers_platform_interface", "version": "6.1.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "audioplayers_linux", "version": "3.1.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_darwin", "version": "5.0.2", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_android", "version": "4.0.3", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}], "configVersion": 1}