.class public interface abstract Lv2/b;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract a(Lw2/l;)Lx2/k;
.end method

.method public abstract b(Lw2/u;I)Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lw2/u;",
            "I)",
            "Ljava/util/Map<",
            "Lw2/l;",
            "Lx2/k;",
            ">;"
        }
    .end annotation
.end method

.method public abstract c(Ljava/util/SortedSet;)Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/SortedSet<",
            "Lw2/l;",
            ">;)",
            "Ljava/util/Map<",
            "Lw2/l;",
            "Lx2/k;",
            ">;"
        }
    .end annotation
.end method

.method public abstract d(I)V
.end method

.method public abstract e(ILjava/util/Map;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/Map<",
            "Lw2/l;",
            "Lx2/f;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract f(Ljava/lang/String;II)Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "II)",
            "Ljava/util/Map<",
            "Lw2/l;",
            "Lx2/k;",
            ">;"
        }
    .end annotation
.end method
