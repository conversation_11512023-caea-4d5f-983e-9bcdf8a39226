.class La/a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = La/a;->l(II)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Ljava/lang/Void;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic a:La/a;


# direct methods
.method constructor <init>(La/a;)V
    .locals 0

    iput-object p1, p0, La/a$a;->a:La/a;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Ljava/lang/Void;
    .locals 3

    const/16 v0, -0x13

    invoke-static {v0}, Landroid/os/Process;->setThreadPriority(I)V

    iget-object v0, p0, La/a$a;->a:La/a;

    invoke-static {v0}, La/a;->a(La/a;)I

    move-result v1

    iget-object v2, p0, La/a$a;->a:La/a;

    invoke-static {v2}, La/a;->b(La/a;)I

    move-result v2

    invoke-static {v0, v1, v2}, La/a;->c(La/a;II)V

    const/4 v0, 0x0

    return-object v0
.end method

.method public bridge synthetic call()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, La/a$a;->a()Ljava/lang/Void;

    move-result-object v0

    return-object v0
.end method
