@echo off
echo ========================================
echo    Complete BonicBot Setup with All Features
echo ========================================
echo.

cd bonic_bot_flutter

echo [1/6] Copying all source files...
if exist "..\lib" (
    xcopy "..\lib\*" "lib\" /E /Y >nul 2>&1
    echo ✓ Source files copied
) else (
    echo ! Source files not found
)

echo [2/6] Creating assets structure...
if not exist "assets" mkdir assets
if not exist "assets\fonts" mkdir assets\fonts
if not exist "assets\lottie" mkdir assets\lottie

echo [3/6] Copying all assets...
REM Main assets
if exist "..\assets\flutter_assets\assets\logo.png" copy "..\assets\flutter_assets\assets\logo.png" "assets\logo.png" >nul 2>&1
if exist "..\assets\flutter_assets\assets\splash.png" copy "..\assets\flutter_assets\assets\splash.png" "assets\splash.png" >nul 2>&1
if exist "..\assets\flutter_assets\assets\f1.jpeg" copy "..\assets\flutter_assets\assets\f1.jpeg" "assets\f1.jpeg" >nul 2>&1
if exist "..\assets\flutter_assets\assets\loading.json" copy "..\assets\flutter_assets\assets\loading.json" "assets\loading.json" >nul 2>&1

REM Audio files
if exist "..\assets\flutter_assets\assets\song1.mp3" copy "..\assets\flutter_assets\assets\song1.mp3" "assets\song1.mp3" >nul 2>&1
if exist "..\assets\flutter_assets\assets\song2.mp3" copy "..\assets\flutter_assets\assets\song2.mp3" "assets\song2.mp3" >nul 2>&1

REM ML models
if exist "..\assets\flutter_assets\assets\mobilefacenet.tflite" copy "..\assets\flutter_assets\assets\mobilefacenet.tflite" "assets\mobilefacenet.tflite" >nul 2>&1

REM Lottie animations
if exist "..\assets\flutter_assets\assets\lottie" xcopy "..\assets\flutter_assets\assets\lottie\*" "assets\lottie\" /Y >nul 2>&1

REM Fonts
if exist "..\assets\flutter_assets\assets\fonts" xcopy "..\assets\flutter_assets\assets\fonts\*" "assets\fonts\" /Y >nul 2>&1

echo ✓ Main assets copied

echo [4/6] Copying wake word models...
REM Primary wake word
if exist "..\assets\flutter_assets\assets\Hey-robot_en_android_v2_2_0.ppn" copy "..\assets\flutter_assets\assets\Hey-robot_en_android_v2_2_0.ppn" "assets\Hey-robot_en_android_v2_2_0.ppn" >nul 2>&1

REM All wake word models from res/raw
if exist "..\res\raw\alexa.ppn" copy "..\res\raw\alexa.ppn" "assets\alexa.ppn" >nul 2>&1
if exist "..\res\raw\americano.ppn" copy "..\res\raw\americano.ppn" "assets\americano.ppn" >nul 2>&1
if exist "..\res\raw\blueberry.ppn" copy "..\res\raw\blueberry.ppn" "assets\blueberry.ppn" >nul 2>&1
if exist "..\res\raw\bumblebee.ppn" copy "..\res\raw\bumblebee.ppn" "assets\bumblebee.ppn" >nul 2>&1
if exist "..\res\raw\computer.ppn" copy "..\res\raw\computer.ppn" "assets\computer.ppn" >nul 2>&1
if exist "..\res\raw\grapefruit.ppn" copy "..\res\raw\grapefruit.ppn" "assets\grapefruit.ppn" >nul 2>&1
if exist "..\res\raw\grasshopper.ppn" copy "..\res\raw\grasshopper.ppn" "assets\grasshopper.ppn" >nul 2>&1
if exist "..\res\raw\hey_barista.ppn" copy "..\res\raw\hey_barista.ppn" "assets\hey_barista.ppn" >nul 2>&1
if exist "..\res\raw\hey_google.ppn" copy "..\res\raw\hey_google.ppn" "assets\hey_google.ppn" >nul 2>&1
if exist "..\res\raw\hey_siri.ppn" copy "..\res\raw\hey_siri.ppn" "assets\hey_siri.ppn" >nul 2>&1
if exist "..\res\raw\jarvis.ppn" copy "..\res\raw\jarvis.ppn" "assets\jarvis.ppn" >nul 2>&1
if exist "..\res\raw\ok_google.ppn" copy "..\res\raw\ok_google.ppn" "assets\ok_google.ppn" >nul 2>&1
if exist "..\res\raw\pico_clock.ppn" copy "..\res\raw\pico_clock.ppn" "assets\pico_clock.ppn" >nul 2>&1
if exist "..\res\raw\picovoice.ppn" copy "..\res\raw\picovoice.ppn" "assets\picovoice.ppn" >nul 2>&1
if exist "..\res\raw\porcupine.ppn" copy "..\res\raw\porcupine.ppn" "assets\porcupine.ppn" >nul 2>&1
if exist "..\res\raw\porcupine_params.pv" copy "..\res\raw\porcupine_params.pv" "assets\porcupine_params.pv" >nul 2>&1
if exist "..\res\raw\terminator.ppn" copy "..\res\raw\terminator.ppn" "assets\terminator.ppn" >nul 2>&1

echo ✓ Wake word models copied

echo [5/6] Installing dependencies...
flutter pub get

echo [6/6] Setup complete!
echo.
echo ========================================
echo    COMPLETE BONICBOT SETUP FINISHED!
echo ========================================
echo.
echo 🎉 Your BonicBot Flutter app now includes ALL features:
echo.
echo ✅ VOICE FEATURES:
echo   • 10+ Wake Words (Hey Robot, Alexa, Jarvis, etc.)
echo   • 50+ Voice Commands
echo   • Text-to-Speech responses
echo   • Voice-controlled music
echo.
echo ✅ CAMERA FEATURES:
echo   • Real-time face detection
echo   • Photo capture
echo   • Video recording
echo   • ML-powered face recognition
echo.
echo ✅ AUDIO FEATURES:
echo   • Music playback (song1.mp3, song2.mp3)
echo   • Volume control
echo   • Mute/unmute
echo   • Voice-controlled audio
echo.
echo ✅ UI FEATURES:
echo   • Lottie animations
echo   • Custom fonts (Poppins)
echo   • Material Design 3
echo   • Responsive layout
echo.
echo ✅ SMART FEATURES:
echo   • Time/date queries
echo   • System commands
echo   • Interactive responses
echo   • Firebase integration
echo.
echo 🚀 To run the app:
echo   flutter run
echo.
echo 🎯 Try these voice commands:
echo   • "Hey Robot, play music"
echo   • "Jarvis, start face detection"
echo   • "Computer, take a photo"
echo   • "Alexa, what time is it?"
echo.
pause
