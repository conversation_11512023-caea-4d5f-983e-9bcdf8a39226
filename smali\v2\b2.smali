.class public final synthetic Lv2/b2;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements La3/n;


# instance fields
.field public final synthetic a:Ljava/util/SortedSet;

.field public final synthetic b:Lw2/q;

.field public final synthetic c:Lw2/l;


# direct methods
.method public synthetic constructor <init>(Ljava/util/SortedSet;Lw2/q;Lw2/l;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lv2/b2;->a:Ljava/util/SortedSet;

    iput-object p2, p0, Lv2/b2;->b:Lw2/q;

    iput-object p3, p0, Lv2/b2;->c:Lw2/l;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lv2/b2;->a:Ljava/util/SortedSet;

    iget-object v1, p0, Lv2/b2;->b:Lw2/q;

    iget-object v2, p0, Lv2/b2;->c:Lw2/l;

    check-cast p1, Landroid/database/Cursor;

    invoke-static {v0, v1, v2, p1}, Lv2/e2;->t(Ljava/util/SortedSet;Lw2/q;Lw2/l;Landroid/database/Cursor;)V

    return-void
.end method
