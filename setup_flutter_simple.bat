@echo off
echo ========================================
echo    Simple Flutter Project Setup
echo ========================================
echo.

echo Step 1: Checking Flutter...
flutter --version
if errorlevel 1 (
    echo.
    echo ERROR: Flutter not found!
    echo Please install Flutter first from: https://flutter.dev
    echo.
    pause
    exit /b 1
)

echo.
echo Step 2: Creating Flutter project...
flutter create --org com.example --project-name bonic_bot bonic_bot_flutter

echo.
echo Step 3: Entering project directory...
cd bonic_bot_flutter

echo.
echo Step 4: Copying pubspec.yaml...
if exist "..\pubspec.yaml" (
    copy "..\pubspec.yaml" "pubspec.yaml"
    echo ✓ pubspec.yaml copied
) else (
    echo ! pubspec.yaml not found in parent directory
)

echo.
echo Step 5: Creating lib directory structure...
if not exist "lib\screens" mkdir lib\screens
if not exist "lib\services" mkdir lib\services
if not exist "lib\widgets" mkdir lib\widgets

echo.
echo Step 6: Copying Dart source files...
if exist "..\lib" (
    xcopy "..\lib\*" "lib\" /E /Y
    echo ✓ Source files copied
) else (
    echo ! lib directory not found in parent directory
)

echo.
echo Step 7: Copying assets...
if exist "..\assets" (
    if not exist "assets" mkdir assets
    xcopy "..\assets\*" "assets\" /E /Y
    echo ✓ Assets copied
) else (
    echo ! assets directory not found in parent directory
)

echo.
echo Step 8: Installing dependencies...
flutter pub get

echo.
echo ========================================
echo           SETUP COMPLETE!
echo ========================================
echo.
echo Project created in: bonic_bot_flutter
echo.
echo To run the app:
echo 1. cd bonic_bot_flutter
echo 2. flutter run
echo.
echo To open in VS Code:
echo   code bonic_bot_flutter
echo.
echo To open in Android Studio:
echo   Open the bonic_bot_flutter folder
echo.
pause
