@echo off
setlocal enabledelayedexpansion
echo ========================================
echo    Creating BonicBot Flutter Project
echo ========================================
echo.

REM Check if Flutter is installed
echo [1/7] Checking Flutter installation...
flutter --version >temp_flutter_check.txt 2>&1
if exist temp_flutter_check.txt (
    findstr /C:"Flutter" temp_flutter_check.txt >nul
    if !errorlevel! equ 0 (
        echo ✓ Flutter found
        del temp_flutter_check.txt
    ) else (
        echo ERROR: Flutter not found!
        echo.
        echo Please install Flutter first:
        echo 1. Download from: https://flutter.dev/docs/get-started/install
        echo 2. Add Flutter to your PATH
        echo 3. Run: flutter doctor
        echo.
        del temp_flutter_check.txt
        pause
        exit /b 1
    )
) else (
    echo ERROR: Cannot check Flutter installation!
    pause
    exit /b 1
)

REM Create Flutter project
echo [2/7] Creating Flutter project...
flutter create --org com.example --project-name bonic_bot bonic_bot_flutter
if !errorlevel! neq 0 (
    echo ERROR: Failed to create Flutter project!
    pause
    exit /b 1
)
echo ✓ Flutter project created

cd bonic_bot_flutter

REM Copy assets from decompiled APK
echo [3/7] Setting up assets...
if exist "..\assets\flutter_assets\assets" (
    if not exist "assets" mkdir assets
    xcopy "..\assets\flutter_assets\assets\*" "assets\" /E /Y >nul
    echo ✓ Assets copied from decompiled APK
) else (
    echo ! Assets not found in parent directory, you'll need to copy them manually
)

REM Setup Android configuration
echo [4/7] Configuring Android settings...
if exist "..\AndroidManifest.xml" (
    copy "..\AndroidManifest.xml" "android\app\src\main\AndroidManifest.xml" >nul
    echo ✓ AndroidManifest.xml copied
)

REM Copy app icons
if exist "..\res\mipmap-hdpi" (
    xcopy "..\res\mipmap-*\*" "android\app\src\main\res\" /E /Y >nul
    echo ✓ App icons copied
)

REM Copy project files
echo [5/7] Copying Flutter project files...
if exist "..\pubspec.yaml" (
    copy "..\pubspec.yaml" "pubspec.yaml" >nul
    echo ✓ pubspec.yaml copied
)

if exist "..\lib" (
    xcopy "..\lib\*" "lib\" /E /Y >nul
    echo ✓ Dart source files copied
)

if exist "..\android\app\src\main\AndroidManifest.xml" (
    copy "..\android\app\src\main\AndroidManifest.xml" "android\app\src\main\AndroidManifest.xml" >nul
    echo ✓ Android manifest updated
)

echo [6/7] Installing dependencies...
flutter pub get
if !errorlevel! neq 0 (
    echo ! Warning: Some dependencies may not be available
    echo   You may need to update pubspec.yaml manually
)

echo [7/7] Project setup complete!
echo.
echo ========================================
echo           SETUP COMPLETE!
echo ========================================
echo.
echo Your BonicBot Flutter project is ready!
echo.
echo Next steps:
echo 1. cd bonic_bot_flutter
echo 2. Connect your Android device or start emulator
echo 3. Run: flutter run
echo 4. Or open in VS Code/Android Studio
echo.
echo Features included:
echo ✓ Voice recognition and TTS
echo ✓ Face detection with camera
echo ✓ Firebase integration
echo ✓ Audio playback
echo ✓ Lottie animations
echo ✓ Material Design UI
echo.
echo The app is a complete recreation of the original BonicBot!
pause
