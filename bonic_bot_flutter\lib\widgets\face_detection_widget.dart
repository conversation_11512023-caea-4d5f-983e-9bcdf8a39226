import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:lottie/lottie.dart';
import '../main.dart';
import '../services/face_detection_service.dart';

class FaceDetectionWidget extends StatefulWidget {
  final bool isActive;

  const FaceDetectionWidget({
    super.key,
    required this.isActive,
  });

  @override
  State<FaceDetectionWidget> createState() => _FaceDetectionWidgetState();
}

class _FaceDetectionWidgetState extends State<FaceDetectionWidget> {
  int _detectedFaces = 0;

  @override
  void initState() {
    super.initState();
    
    // Listen for face detection events
    FaceDetectionService.onFaceDetected = (faceCount) {
      if (mounted) {
        setState(() {
          _detectedFaces = faceCount;
        });
      }
    };
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Camera preview or placeholder
          Expanded(
            flex: 3,
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              child: widget.isActive ? _buildCameraPreview() : _buildPlaceholder(),
            ),
          ),
          
          // Face detection info
          Expanded(
            flex: 1,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.face,
                        color: widget.isActive ? AppColors.primary : AppColors.onSurface.withOpacity(0.5),
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Faces Detected: $_detectedFaces',
                        style: AppTextStyles.heading2.copyWith(
                          color: widget.isActive ? AppColors.primary : AppColors.onSurface.withOpacity(0.5),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    widget.isActive 
                        ? 'Face detection is active'
                        : 'Face detection is inactive',
                    style: AppTextStyles.body2.copyWith(
                      color: AppColors.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraPreview() {
    final cameraController = FaceDetectionService.cameraController;
    
    if (cameraController != null && cameraController.value.isInitialized) {
      return Stack(
        children: [
          // Camera preview
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: CameraPreview(cameraController),
          ),
          
          // Face detection overlay
          if (_detectedFaces > 0) _buildFaceOverlay(),
          
          // Status indicator
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'LIVE',
                    style: AppTextStyles.body2.copyWith(
                      color: AppColors.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    } else {
      return _buildCameraError();
    }
  }

  Widget _buildPlaceholder() {
    return Container(
      width: double.infinity,
      color: AppColors.background,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Face detection animation
          SizedBox(
            width: 120,
            height: 120,
            child: Lottie.asset(
              'assets/lottie/image.json',
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.face,
                    size: 60,
                    color: AppColors.primary.withOpacity(0.5),
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(height: 20),
          
          Text(
            'Face Detection Ready',
            style: AppTextStyles.heading2.copyWith(
              color: AppColors.onSurface.withOpacity(0.7),
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Tap "Start Detection" to begin',
            style: AppTextStyles.body1.copyWith(
              color: AppColors.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraError() {
    return Container(
      width: double.infinity,
      color: AppColors.error.withOpacity(0.1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.camera_alt_outlined,
            size: 60,
            color: AppColors.error,
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'Camera Error',
            style: AppTextStyles.heading2.copyWith(
              color: AppColors.error,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Unable to access camera',
            style: AppTextStyles.body1.copyWith(
              color: AppColors.error.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFaceOverlay() {
    return Positioned.fill(
      child: CustomPaint(
        painter: FaceOverlayPainter(_detectedFaces),
      ),
    );
  }
}

class FaceOverlayPainter extends CustomPainter {
  final int faceCount;

  FaceOverlayPainter(this.faceCount);

  @override
  void paint(Canvas canvas, Size size) {
    if (faceCount == 0) return;

    final paint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    // Draw a simple rectangle overlay for detected faces
    // In a real implementation, you would use the actual face bounds
    final rect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: size.width * 0.6,
      height: size.height * 0.6,
    );

    canvas.drawRect(rect, paint);

    // Draw face count
    final textPainter = TextPainter(
      text: TextSpan(
        text: '$faceCount',
        style: const TextStyle(
          color: AppColors.primary,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        rect.left - 30,
        rect.top - 30,
      ),
    );
  }

  @override
  bool shouldRepaint(FaceOverlayPainter oldDelegate) {
    return oldDelegate.faceCount != faceCount;
  }
}
