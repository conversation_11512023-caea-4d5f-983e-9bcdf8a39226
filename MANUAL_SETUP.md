# Manual Flutter Setup Guide

If the batch files are not working, follow these manual steps to create your Flutter project.

## Prerequisites

1. **Install Flutter SDK**
   - Download from: https://flutter.dev/docs/get-started/install/windows
   - Extract to `C:\flutter` (or your preferred location)
   - Add `C:\flutter\bin` to your system PATH
   - Run `flutter doctor` to verify installation

2. **Install Android Studio** (recommended)
   - Download from: https://developer.android.com/studio
   - Install Android SDK and emulator

3. **Install VS Code** (optional but recommended)
   - Download from: https://code.visualstudio.com/
   - Install Flutter and Dart extensions

## Step-by-Step Manual Setup

### Step 1: Create Flutter Project
```bash
# Open Command Prompt or PowerShell
# Navigate to your desired directory
cd "C:\Users\<USER>\Downloads\Phone Link\output"

# Create new Flutter project
flutter create --org com.example --project-name bonic_bot bonic_bot_flutter

# Enter the project directory
cd bonic_bot_flutter
```

### Step 2: Replace pubspec.yaml
1. Open the `pubspec.yaml` file in the project
2. Replace its contents with the `pubspec.yaml` file I created
3. Save the file

### Step 3: Copy Source Code
1. Delete the default `lib/main.dart` file
2. Copy all files from the `lib/` folder I created to your project's `lib/` folder
3. Make sure you have these files:
   - `lib/main.dart`
   - `lib/screens/splash_screen.dart`
   - `lib/screens/home_screen.dart`
   - `lib/services/voice_service.dart`
   - `lib/services/face_detection_service.dart`
   - `lib/services/audio_service.dart`
   - `lib/services/firebase_service.dart`
   - `lib/widgets/voice_animation_widget.dart`
   - `lib/widgets/face_detection_widget.dart`
   - `lib/widgets/control_panel_widget.dart`

### Step 4: Copy Assets
1. Create an `assets/` folder in your project root
2. Copy assets from your decompiled APK:
   - From `assets/flutter_assets/assets/` → to `assets/`
   - This includes images, audio files, fonts, animations, etc.

### Step 5: Update Android Configuration
1. Replace `android/app/src/main/AndroidManifest.xml` with the one I created
2. Copy app icons from `res/mipmap-*` folders to `android/app/src/main/res/`

### Step 6: Install Dependencies
```bash
# In your project directory
flutter pub get
```

### Step 7: Run the App
```bash
# Connect Android device or start emulator
flutter devices

# Run the app
flutter run
```

## Troubleshooting

### Flutter Not Found
```bash
# Check if Flutter is in PATH
flutter --version

# If not found, add Flutter to PATH:
# 1. Open System Properties → Environment Variables
# 2. Add C:\flutter\bin to PATH
# 3. Restart Command Prompt
```

### Dependencies Not Installing
```bash
# Clear Flutter cache
flutter clean
flutter pub cache repair

# Try installing again
flutter pub get
```

### Android Issues
```bash
# Check Android setup
flutter doctor

# Accept Android licenses
flutter doctor --android-licenses
```

### Emulator Issues
```bash
# List available emulators
flutter emulators

# Start an emulator
flutter emulators --launch <emulator_id>
```

## Project Structure After Setup

```
bonic_bot_flutter/
├── android/
│   └── app/src/main/
│       ├── AndroidManifest.xml
│       └── res/
├── assets/
│   ├── fonts/
│   ├── lottie/
│   ├── *.mp3
│   ├── *.png
│   └── *.ppn
├── lib/
│   ├── main.dart
│   ├── screens/
│   ├── services/
│   └── widgets/
├── pubspec.yaml
└── README.md
```

## Testing the Setup

1. **Check Dependencies**:
   ```bash
   flutter pub deps
   ```

2. **Analyze Code**:
   ```bash
   flutter analyze
   ```

3. **Run Tests**:
   ```bash
   flutter test
   ```

4. **Build APK**:
   ```bash
   flutter build apk --debug
   ```

## Alternative: Use VS Code

1. Open VS Code
2. Install Flutter and Dart extensions
3. Press `Ctrl+Shift+P`
4. Type "Flutter: New Project"
5. Follow the wizard
6. Manually copy the files as described above

## Alternative: Use Android Studio

1. Open Android Studio
2. File → New → New Flutter Project
3. Choose "Flutter Application"
4. Set project name to "bonic_bot"
5. Set package name to "com.example.bonic_bot"
6. Manually copy the files as described above

## Final Verification

After setup, your app should:
- ✅ Compile without errors
- ✅ Show splash screen with BonicBot logo
- ✅ Display main interface with 3 tabs
- ✅ Request permissions for camera/microphone
- ✅ Show voice animation when listening
- ✅ Display camera preview for face detection

If you encounter any issues, check the troubleshooting section or run `flutter doctor` for diagnostics.
