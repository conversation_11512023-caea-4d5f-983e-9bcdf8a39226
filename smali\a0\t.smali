.class public final enum La0/t;
.super Ljava/lang/Enum;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "La0/t;",
        ">;"
    }
.end annotation


# static fields
.field public static final enum a:La0/t;

.field public static final enum b:La0/t;

.field public static final enum c:La0/t;

.field public static final enum d:La0/t;

.field public static final enum e:La0/t;

.field public static final enum j:La0/t;

.field private static final synthetic k:[La0/t;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    new-instance v0, La0/t;

    const-string v1, "listening"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, La0/t;-><init>(Ljava/lang/String;I)V

    sput-object v0, La0/t;->a:La0/t;

    new-instance v0, La0/t;

    const-string v1, "notListening"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, La0/t;-><init>(Ljava/lang/String;I)V

    sput-object v0, La0/t;->b:La0/t;

    new-instance v0, La0/t;

    const-string v1, "unavailable"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, La0/t;-><init>(Ljava/lang/String;I)V

    sput-object v0, La0/t;->c:La0/t;

    new-instance v0, La0/t;

    const-string v1, "available"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, La0/t;-><init>(Ljava/lang/String;I)V

    sput-object v0, La0/t;->d:La0/t;

    new-instance v0, La0/t;

    const-string v1, "done"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, La0/t;-><init>(Ljava/lang/String;I)V

    sput-object v0, La0/t;->e:La0/t;

    new-instance v0, La0/t;

    const-string v1, "doneNoResult"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, La0/t;-><init>(Ljava/lang/String;I)V

    sput-object v0, La0/t;->j:La0/t;

    invoke-static {}, La0/t;->a()[La0/t;

    move-result-object v0

    sput-object v0, La0/t;->k:[La0/t;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method private static final synthetic a()[La0/t;
    .locals 3

    const/4 v0, 0x6

    new-array v0, v0, [La0/t;

    sget-object v1, La0/t;->a:La0/t;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    sget-object v1, La0/t;->b:La0/t;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    sget-object v1, La0/t;->c:La0/t;

    const/4 v2, 0x2

    aput-object v1, v0, v2

    sget-object v1, La0/t;->d:La0/t;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    sget-object v1, La0/t;->e:La0/t;

    const/4 v2, 0x4

    aput-object v1, v0, v2

    sget-object v1, La0/t;->j:La0/t;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    return-object v0
.end method

.method public static valueOf(Ljava/lang/String;)La0/t;
    .locals 1

    const-class v0, La0/t;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, La0/t;

    return-object p0
.end method

.method public static values()[La0/t;
    .locals 1

    sget-object v0, La0/t;->k:[La0/t;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [La0/t;

    return-object v0
.end method
