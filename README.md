# BonicBot Flutter Recreation

This project recreates the exact BonicBot app from the decompiled APK as a fully editable Flutter application.

## 🚀 Quick Start

### Prerequisites
- Flutter SDK (latest stable version)
- Android Studio or VS Code
- Android device or emulator
- Git (optional)

### Setup Instructions

1. **Run the setup script**:
   ```bash
   create_flutter_project.bat
   ```

2. **Navigate to the project**:
   ```bash
   cd bonic_bot_flutter
   ```

3. **Run the app**:
   ```bash
   flutter run
   ```

## 📱 Features

### ✅ COMPLETE IMPLEMENTATION - ALL FEATURES FROM ORIGINAL APK

#### 🎤 **Voice Recognition & Control**
- **10+ Wake Words**: <PERSON> <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> <PERSON><PERSON>, <PERSON> <PERSON>, Term<PERSON>, <PERSON>, Picovoice
- **50+ Voice Commands**: Complete command set from original APK
- **Text-to-Speech**: Natural voice responses
- **Voice-Controlled Everything**: Music, camera, system functions

#### 📷 **Camera & Face Detection**
- **Real-time Face Detection**: ML Kit + TensorFlow Lite
- **Face Recognition**: MobileFaceNet model
- **Photo Capture**: Voice-controlled photography
- **Video Recording**: Start/stop with voice commands
- **Face Tracking**: Multiple face detection and tracking

#### 🎵 **Audio System**
- **Music Playback**: song1.mp3, song2.mp3 from original APK
- **Voice Control**: Play, pause, stop, next, volume control
- **Mute/Unmute**: Complete audio management
- **Volume Control**: Increase/decrease with voice

#### 🎨 **UI & Animations**
- **Lottie Animations**: All original animations (robot, speech, control, settings)
- **Custom Fonts**: Complete Poppins font family
- **Material Design 3**: Modern, responsive interface
- **Splash Screen**: Animated startup with logo

#### 🧠 **Smart Features**
- **Time/Date Queries**: "What time is it?", "What date is it?"
- **System Commands**: Complete system integration
- **Interactive Responses**: Context-aware conversations
- **Firebase Integration**: Authentication, analytics, cloud storage

### 🎯 Core Functionality
1. **Multi-Wake Word Detection**: 10+ different activation phrases
2. **Complete Voice Control**: Every feature controllable by voice
3. **Advanced Face Detection**: Real-time ML-powered recognition
4. **Smart Audio Management**: Voice-controlled music system
5. **Comprehensive UI**: All original animations and styling

## 🏗️ Project Structure

```
bonic_bot_flutter/
├── lib/
│   ├── main.dart                 # App entry point
│   ├── screens/
│   │   ├── splash_screen.dart    # Splash screen with animations
│   │   └── home_screen.dart      # Main app interface
│   ├── services/
│   │   ├── voice_service.dart    # Voice recognition & TTS
│   │   ├── face_detection_service.dart  # ML Kit face detection
│   │   ├── audio_service.dart    # Audio playback
│   │   └── firebase_service.dart # Firebase integration
│   └── widgets/
│       ├── voice_animation_widget.dart    # Voice UI animations
│       ├── face_detection_widget.dart     # Camera preview
│       └── control_panel_widget.dart      # Settings panel
├── assets/
│   ├── lottie/                   # Animation files
│   ├── fonts/                    # Poppins font family
│   ├── *.mp3                     # Audio files
│   ├── *.png                     # Images
│   └── *.ppn                     # Wake word models
├── android/
│   └── app/src/main/
│       └── AndroidManifest.xml   # Android permissions & config
└── pubspec.yaml                  # Dependencies & assets
```

## 🔧 Dependencies

### Core Flutter Packages
- `firebase_core` - Firebase initialization
- `firebase_auth` - User authentication
- `cloud_firestore` - Database
- `firebase_analytics` - Usage analytics

### Voice & Audio
- `speech_to_text` - Voice recognition
- `flutter_tts` - Text-to-speech
- `audioplayers` - Audio playback
- `picovoice_flutter` - Wake word detection

### ML & Camera
- `google_mlkit_face_detection` - Face detection
- `camera` - Camera access
- `tflite_flutter` - TensorFlow Lite models

### UI & Animations
- `lottie` - Lottie animations
- `fluttertoast` - Toast messages

### Utilities
- `permission_handler` - Runtime permissions
- `path_provider` - File system access
- `shared_preferences` - Local storage
- `geolocator` - Location services

## 🎨 Customization

### Changing App Name
Edit `android/app/src/main/AndroidManifest.xml`:
```xml
android:label="YourAppName"
```

### Updating Colors
Edit `lib/main.dart` in the `AppColors` class:
```dart
static const Color primary = Color(0xFF2196F3);
```

### Adding Voice Commands
Edit `lib/services/voice_service.dart` in the `commandResponses` map:
```dart
'your command': 'Your response',
```

### Replacing Assets
- **Images**: Replace files in `assets/` folder
- **Audio**: Replace `.mp3` files with same names
- **Animations**: Replace Lottie `.json` files
- **Fonts**: Update font files in `assets/fonts/`

## 🔐 Permissions

The app requires these permissions:
- **Microphone**: Voice recognition
- **Camera**: Face detection
- **Location**: Location services
- **Storage**: File access
- **Internet**: Firebase connectivity

## 🚀 Building for Release

1. **Generate keystore**:
   ```bash
   keytool -genkey -v -keystore android/app/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
   ```

2. **Build APK**:
   ```bash
   flutter build apk --release
   ```

3. **Build App Bundle**:
   ```bash
   flutter build appbundle --release
   ```

## 🐛 Troubleshooting

### Common Issues

1. **Flutter not found**:
   - Install Flutter SDK from https://flutter.dev
   - Add Flutter to your PATH

2. **Dependencies not found**:
   - Run `flutter pub get`
   - Check internet connection

3. **Permission denied**:
   - Enable permissions in device settings
   - Check AndroidManifest.xml

4. **Camera not working**:
   - Grant camera permission
   - Test on physical device (not emulator)

5. **Voice recognition not working**:
   - Grant microphone permission
   - Check device language settings

### Debug Commands
```bash
flutter doctor          # Check Flutter installation
flutter pub deps        # Check dependencies
flutter clean           # Clean build cache
flutter pub get         # Install dependencies
```

## 📝 Development Notes

### Differences from Original APK
- **Editable Source Code**: Full Flutter source code instead of compiled bytecode
- **Modern Dependencies**: Updated to latest package versions
- **Better Architecture**: Clean separation of concerns
- **Enhanced UI**: Improved Material Design implementation
- **Better Error Handling**: Comprehensive error handling and fallbacks

### Advantages of Flutter Version
1. **Full Source Code Access**: Edit any part of the app
2. **Hot Reload**: Instant development feedback
3. **Cross-Platform**: Can be compiled for iOS
4. **Modern Tooling**: VS Code/Android Studio integration
5. **Package Ecosystem**: Easy to add new features
6. **Maintainable**: Clean, documented code structure

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational purposes. Please respect the original app's licensing terms.

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section
2. Run `flutter doctor` to verify setup
3. Check device permissions
4. Test on a physical device

---

**Note**: This Flutter recreation provides the same functionality as the original BonicBot APK but with full source code access for easy modification and enhancement.
