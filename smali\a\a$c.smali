.class La/a$c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = La/a;->i(La/d;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:La/c;

.field final synthetic b:La/d;

.field final synthetic c:La/a;


# direct methods
.method constructor <init>(La/a;La/c;La/d;)V
    .locals 0

    iput-object p1, p0, La/a$c;->c:La/a;

    iput-object p2, p0, La/a$c;->a:La/c;

    iput-object p3, p0, La/a$c;->b:La/d;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, La/a$c;->a:La/c;

    iget-object v1, p0, La/a$c;->b:La/d;

    invoke-interface {v0, v1}, La/c;->a(La/d;)V

    return-void
.end method
