import 'dart:io';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:tflite_flutter/tflite_flutter.dart';

class FaceDetectionService {
  static FaceDetector? _faceDetector;
  static Interpreter? _interpreter;
  static CameraController? _cameraController;
  static List<CameraDescription>? _cameras;
  
  static Function(int)? onFaceDetected;
  static Function(List<Face>)? onFacesDetected;
  
  static bool _isInitialized = false;
  static bool _isDetecting = false;

  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialize ML Kit Face Detection
      _faceDetector = FaceDetector(
        options: FaceDetectorOptions(
          enableContours: true,
          enableLandmarks: true,
          enableClassification: true,
          enableTracking: true,
          minFaceSize: 0.1,
          performanceMode: FaceDetectorMode.accurate,
        ),
      );
      
      // Initialize TensorFlow Lite model for face recognition
      await _initializeTFLiteModel();
      
      // Initialize camera
      await _initializeCamera();
      
      _isInitialized = true;
      print('Face detection service initialized successfully');
    } catch (e) {
      print('Error initializing face detection service: $e');
    }
  }

  static Future<void> _initializeTFLiteModel() async {
    try {
      // Load the face recognition model
      _interpreter = await Interpreter.fromAsset('assets/mobilefacenet.tflite');
      print('TensorFlow Lite model loaded successfully');
    } catch (e) {
      print('Error loading TensorFlow Lite model: $e');
    }
  }

  static Future<void> _initializeCamera() async {
    try {
      _cameras = await availableCameras();
      if (_cameras!.isNotEmpty) {
        _cameraController = CameraController(
          _cameras![1], // Front camera
          ResolutionPreset.medium,
          enableAudio: false,
        );
        await _cameraController!.initialize();
        print('Camera initialized successfully');
      }
    } catch (e) {
      print('Error initializing camera: $e');
    }
  }

  static Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting) return;
    
    try {
      _isDetecting = true;
      
      if (_cameraController != null && _cameraController!.value.isInitialized) {
        await _cameraController!.startImageStream(_processCameraImage);
        print('Face detection started');
      }
    } catch (e) {
      print('Error starting face detection: $e');
      _isDetecting = false;
    }
  }

  static Future<void> stopDetection() async {
    if (!_isDetecting) return;
    
    try {
      if (_cameraController != null) {
        await _cameraController!.stopImageStream();
      }
      _isDetecting = false;
      print('Face detection stopped');
    } catch (e) {
      print('Error stopping face detection: $e');
    }
  }

  static void _processCameraImage(CameraImage image) async {
    if (!_isDetecting) return;
    
    try {
      // Convert camera image to InputImage for ML Kit
      final inputImage = _convertCameraImage(image);
      if (inputImage == null) return;
      
      // Detect faces using ML Kit
      final faces = await _faceDetector!.processImage(inputImage);
      
      // Notify listeners
      onFaceDetected?.call(faces.length);
      onFacesDetected?.call(faces);
      
      // Process each detected face
      for (Face face in faces) {
        await _processFace(face, image);
      }
      
    } catch (e) {
      print('Error processing camera image: $e');
    }
  }

  static InputImage? _convertCameraImage(CameraImage image) {
    try {
      final WriteBuffer allBytes = WriteBuffer();
      for (Plane plane in image.planes) {
        allBytes.putUint8List(plane.bytes);
      }
      final bytes = allBytes.done().buffer.asUint8List();
      
      final Size imageSize = Size(
        image.width.toDouble(),
        image.height.toDouble(),
      );
      
      final camera = _cameras![1]; // Front camera
      final imageRotation = InputImageRotationValue.fromRawValue(
        camera.sensorOrientation,
      );
      
      if (imageRotation == null) return null;
      
      final inputImageFormat = InputImageFormatValue.fromRawValue(
        image.format.raw,
      );
      
      if (inputImageFormat == null) return null;
      
      final planeData = image.planes.map(
        (Plane plane) {
          return InputImagePlaneMetadata(
            bytesPerRow: plane.bytesPerRow,
            height: plane.height,
            width: plane.width,
          );
        },
      ).toList();
      
      final inputImageData = InputImageData(
        size: imageSize,
        imageRotation: imageRotation,
        inputImageFormat: inputImageFormat,
        planeData: planeData,
      );
      
      return InputImage.fromBytes(
        bytes: bytes,
        inputImageData: inputImageData,
      );
    } catch (e) {
      print('Error converting camera image: $e');
      return null;
    }
  }

  static Future<void> _processFace(Face face, CameraImage image) async {
    try {
      // Extract face features using TensorFlow Lite model
      if (_interpreter != null) {
        // Prepare input for the model
        final input = _prepareFaceInput(face, image);
        if (input != null) {
          // Run inference
          final output = List.filled(128, 0.0).reshape([1, 128]);
          _interpreter!.run(input, output);
          
          // Process the face embedding
          final embedding = output[0] as List<double>;
          await _processFaceEmbedding(embedding, face);
        }
      }
    } catch (e) {
      print('Error processing face: $e');
    }
  }

  static List<List<List<List<double>>>>? _prepareFaceInput(
    Face face,
    CameraImage image,
  ) {
    try {
      // This is a simplified version - in a real implementation,
      // you would crop the face from the image and resize it to 112x112
      // for the MobileFaceNet model
      
      // For now, return a dummy input
      return List.generate(
        1,
        (i) => List.generate(
          112,
          (j) => List.generate(
            112,
            (k) => List.generate(3, (l) => 0.0),
          ),
        ),
      );
    } catch (e) {
      print('Error preparing face input: $e');
      return null;
    }
  }

  static Future<void> _processFaceEmbedding(
    List<double> embedding,
    Face face,
  ) async {
    try {
      // Process the face embedding for recognition
      // This could involve comparing with stored embeddings
      // for face recognition functionality
      
      print('Face embedding processed: ${embedding.length} features');
      
      // You can implement face recognition logic here
      // by comparing embeddings with a database of known faces
      
    } catch (e) {
      print('Error processing face embedding: $e');
    }
  }

  static CameraController? get cameraController => _cameraController;
  static bool get isDetecting => _isDetecting;
  static bool get isInitialized => _isInitialized;

  static Future<void> dispose() async {
    try {
      await stopDetection();
      await _cameraController?.dispose();
      await _faceDetector?.close();
      _interpreter?.close();
      _isInitialized = false;
    } catch (e) {
      print('Error disposing face detection service: $e');
    }
  }
}
