.class public final synthetic Lv2/c3;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements La3/u;


# instance fields
.field public final synthetic a:Lt2/b1;

.field public final synthetic b:Ljava/util/Set;


# direct methods
.method public synthetic constructor <init>(Lt2/b1;Ljava/util/Set;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lv2/c3;->a:Lt2/b1;

    iput-object p2, p0, Lv2/c3;->b:Ljava/util/Set;

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lv2/c3;->a:Lt2/b1;

    iget-object v1, p0, Lv2/c3;->b:Ljava/util/Set;

    check-cast p1, Lw2/s;

    invoke-static {v0, v1, p1}, Lv2/g3;->i(Lt2/b1;<PERSON><PERSON><PERSON>/util/Set;Lw2/s;)<PERSON><PERSON><PERSON>/lang/Boolean;

    move-result-object p1

    return-object p1
.end method
