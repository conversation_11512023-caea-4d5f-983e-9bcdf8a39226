Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Creating BonicBot Flutter Project" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Check Flutter installation
Write-Host "[1/7] Checking Flutter installation..." -ForegroundColor Yellow
try {
    $flutterVersion = flutter --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Flutter found" -ForegroundColor Green
        Write-Host $flutterVersion[0] -ForegroundColor Gray
    } else {
        throw "Flutter command failed"
    }
} catch {
    Write-Host "ERROR: Flutter not found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Flutter first:" -ForegroundColor Yellow
    Write-Host "1. Download from: https://flutter.dev/docs/get-started/install" -ForegroundColor White
    Write-Host "2. Add Flutter to your PATH" -ForegroundColor White
    Write-Host "3. Run: flutter doctor" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 2: Create Flutter project
Write-Host ""
Write-Host "[2/7] Creating Flutter project..." -ForegroundColor Yellow
try {
    flutter create --org com.example --project-name bonic_bot bonic_bot_flutter
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Flutter project created" -ForegroundColor Green
    } else {
        throw "Failed to create Flutter project"
    }
} catch {
    Write-Host "ERROR: Failed to create Flutter project!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 3: Enter project directory
Set-Location bonic_bot_flutter

# Step 4: Copy assets from decompiled APK
Write-Host ""
Write-Host "[3/7] Setting up assets..." -ForegroundColor Yellow
if (Test-Path "..\assets\flutter_assets\assets") {
    if (!(Test-Path "assets")) {
        New-Item -ItemType Directory -Path "assets" | Out-Null
    }
    Copy-Item "..\assets\flutter_assets\assets\*" "assets\" -Recurse -Force
    Write-Host "✓ Assets copied from decompiled APK" -ForegroundColor Green
} else {
    Write-Host "! Assets not found in parent directory, you'll need to copy them manually" -ForegroundColor Yellow
}

# Step 5: Setup Android configuration
Write-Host ""
Write-Host "[4/7] Configuring Android settings..." -ForegroundColor Yellow
if (Test-Path "..\AndroidManifest.xml") {
    Copy-Item "..\AndroidManifest.xml" "android\app\src\main\AndroidManifest.xml" -Force
    Write-Host "✓ AndroidManifest.xml copied" -ForegroundColor Green
}

# Copy app icons
if (Test-Path "..\res\mipmap-hdpi") {
    Copy-Item "..\res\mipmap-*\*" "android\app\src\main\res\" -Recurse -Force
    Write-Host "✓ App icons copied" -ForegroundColor Green
}

# Step 6: Copy project files
Write-Host ""
Write-Host "[5/7] Copying Flutter project files..." -ForegroundColor Yellow
if (Test-Path "..\pubspec.yaml") {
    Copy-Item "..\pubspec.yaml" "pubspec.yaml" -Force
    Write-Host "✓ pubspec.yaml copied" -ForegroundColor Green
}

if (Test-Path "..\lib") {
    Copy-Item "..\lib\*" "lib\" -Recurse -Force
    Write-Host "✓ Dart source files copied" -ForegroundColor Green
}

if (Test-Path "..\android\app\src\main\AndroidManifest.xml") {
    Copy-Item "..\android\app\src\main\AndroidManifest.xml" "android\app\src\main\AndroidManifest.xml" -Force
    Write-Host "✓ Android manifest updated" -ForegroundColor Green
}

# Step 7: Install dependencies
Write-Host ""
Write-Host "[6/7] Installing dependencies..." -ForegroundColor Yellow
try {
    flutter pub get
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Dependencies installed" -ForegroundColor Green
    } else {
        Write-Host "! Warning: Some dependencies may not be available" -ForegroundColor Yellow
        Write-Host "  You may need to update pubspec.yaml manually" -ForegroundColor Yellow
    }
} catch {
    Write-Host "! Warning: Error installing dependencies" -ForegroundColor Yellow
}

# Step 8: Final setup
Write-Host ""
Write-Host "[7/7] Project setup complete!" -ForegroundColor Yellow
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           SETUP COMPLETE!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Your BonicBot Flutter project is ready!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Connect your Android device or start emulator" -ForegroundColor White
Write-Host "2. Run: flutter run" -ForegroundColor White
Write-Host "3. Or open in VS Code: code ." -ForegroundColor White
Write-Host "4. Or open in Android Studio" -ForegroundColor White
Write-Host ""
Write-Host "Features included:" -ForegroundColor Yellow
Write-Host "✓ Voice recognition and TTS" -ForegroundColor Green
Write-Host "✓ Face detection with camera" -ForegroundColor Green
Write-Host "✓ Firebase integration" -ForegroundColor Green
Write-Host "✓ Audio playback" -ForegroundColor Green
Write-Host "✓ Lottie animations" -ForegroundColor Green
Write-Host "✓ Material Design UI" -ForegroundColor Green
Write-Host ""
Write-Host "The app is a complete recreation of the original BonicBot!" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to continue"
