import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../main.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _startSplashSequence();
  }

  void _startSplashSequence() async {
    // Start fade animation
    _animationController.forward();
    
    // Wait for splash duration
    await Future.delayed(const Duration(seconds: 3));
    
    // Navigate to home screen
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/home');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Center(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo
              Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(75),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(75),
                  child: Image.asset(
                    'assets/logo.png',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          color: AppColors.surface,
                          borderRadius: BorderRadius.circular(75),
                        ),
                        child: const Icon(
                          Icons.android,
                          size: 80,
                          color: AppColors.primary,
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              const SizedBox(height: 30),
              
              // App Name
              Text(
                'BonicBot',
                style: AppTextStyles.heading1.copyWith(
                  color: AppColors.onPrimary,
                  fontSize: 36,
                ),
              ),
              
              const SizedBox(height: 10),
              
              // Tagline
              Text(
                'AI Voice Assistant',
                style: AppTextStyles.body1.copyWith(
                  color: AppColors.onPrimary.withOpacity(0.8),
                  fontSize: 18,
                ),
              ),
              
              const SizedBox(height: 50),
              
              // Loading Animation
              SizedBox(
                width: 100,
                height: 100,
                child: Lottie.asset(
                  'assets/loading.json',
                  errorBuilder: (context, error, stackTrace) {
                    return const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.onPrimary,
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 20),
              
              Text(
                'Initializing...',
                style: AppTextStyles.body2.copyWith(
                  color: AppColors.onPrimary.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
