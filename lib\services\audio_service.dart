import 'package:audioplayers/audioplayers.dart';

class AudioService {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static bool _isPlaying = false;
  static String? _currentTrack;

  static double _currentVolume = 0.5;
  static bool _isMuted = false;
  static double _volumeBeforeMute = 0.5;

  static Future<void> initialize() async {
    try {
      // Set up audio player listeners
      _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
        _isPlaying = state == PlayerState.playing;
        print('Audio player state: $state');
      });

      _audioPlayer.onDurationChanged.listen((Duration duration) {
        print('Audio duration: $duration');
      });

      _audioPlayer.onPositionChanged.listen((Duration position) {
        // Handle position changes for progress tracking
      });

      // Set initial volume
      await _audioPlayer.setVolume(_currentVolume);

      print('Audio service initialized');
    } catch (e) {
      print('Error initializing audio service: $e');
    }
  }

  static Future<void> playMusic([String? trackPath]) async {
    try {
      trackPath ??= 'assets/song1.mp3';
      
      if (_isPlaying && _currentTrack == trackPath) {
        // Already playing this track
        return;
      }

      await _audioPlayer.stop();
      await _audioPlayer.play(AssetSource(trackPath));
      _currentTrack = trackPath;
      
      print('Playing: $trackPath');
    } catch (e) {
      print('Error playing music: $e');
    }
  }

  static Future<void> playSecondTrack() async {
    await playMusic('assets/song2.mp3');
  }

  static Future<void> pauseMusic() async {
    try {
      await _audioPlayer.pause();
      print('Music paused');
    } catch (e) {
      print('Error pausing music: $e');
    }
  }

  static Future<void> resumeMusic() async {
    try {
      await _audioPlayer.resume();
      print('Music resumed');
    } catch (e) {
      print('Error resuming music: $e');
    }
  }

  static Future<void> stopMusic() async {
    try {
      await _audioPlayer.stop();
      _currentTrack = null;
      print('Music stopped');
    } catch (e) {
      print('Error stopping music: $e');
    }
  }

  static Future<void> setVolume(double volume) async {
    try {
      _currentVolume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(_currentVolume);
      print('Volume set to: ${(_currentVolume * 100).round()}%');
    } catch (e) {
      print('Error setting volume: $e');
    }
  }

  static Future<void> increaseVolume() async {
    final newVolume = (_currentVolume + 0.1).clamp(0.0, 1.0);
    await setVolume(newVolume);
  }

  static Future<void> decreaseVolume() async {
    final newVolume = (_currentVolume - 0.1).clamp(0.0, 1.0);
    await setVolume(newVolume);
  }

  static Future<void> mute() async {
    try {
      if (!_isMuted) {
        _volumeBeforeMute = _currentVolume;
        await _audioPlayer.setVolume(0.0);
        _isMuted = true;
        print('Audio muted');
      }
    } catch (e) {
      print('Error muting audio: $e');
    }
  }

  static Future<void> unmute() async {
    try {
      if (_isMuted) {
        await _audioPlayer.setVolume(_volumeBeforeMute);
        _currentVolume = _volumeBeforeMute;
        _isMuted = false;
        print('Audio unmuted');
      }
    } catch (e) {
      print('Error unmuting audio: $e');
    }
  }

  static Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      print('Error seeking: $e');
    }
  }

  static bool get isPlaying => _isPlaying;
  static String? get currentTrack => _currentTrack;
  static double get currentVolume => _currentVolume;
  static bool get isMuted => _isMuted;

  static Future<void> dispose() async {
    try {
      await _audioPlayer.dispose();
    } catch (e) {
      print('Error disposing audio service: $e');
    }
  }
}

// Audio file manager for handling different audio assets
class AudioAssets {
  static const String song1 = 'assets/song1.mp3';
  static const String song2 = 'assets/song2.mp3';
  
  static const List<String> allTracks = [
    song1,
    song2,
  ];
  
  static String getTrackName(String path) {
    switch (path) {
      case song1:
        return 'Song 1';
      case song2:
        return 'Song 2';
      default:
        return 'Unknown Track';
    }
  }
}
