import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

class FirebaseService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Authentication
  static User? get currentUser => _auth.currentUser;
  static bool get isSignedIn => currentUser != null;

  static Future<UserCredential?> signInAnonymously() async {
    try {
      final credential = await _auth.signInAnonymously();
      await _analytics.logLogin(loginMethod: 'anonymous');
      return credential;
    } catch (e) {
      print('Error signing in anonymously: $e');
      return null;
    }
  }

  static Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      print('Error signing out: $e');
    }
  }

  // Firestore operations
  static Future<void> saveUserData(Map<String, dynamic> data) async {
    try {
      if (currentUser != null) {
        await _firestore
            .collection('users')
            .doc(currentUser!.uid)
            .set(data, SetOptions(merge: true));
      }
    } catch (e) {
      print('Error saving user data: $e');
    }
  }

  static Future<Map<String, dynamic>?> getUserData() async {
    try {
      if (currentUser != null) {
        final doc = await _firestore
            .collection('users')
            .doc(currentUser!.uid)
            .get();
        return doc.data();
      }
    } catch (e) {
      print('Error getting user data: $e');
    }
    return null;
  }

  static Future<void> saveVoiceCommand(String command, String response) async {
    try {
      if (currentUser != null) {
        await _firestore
            .collection('users')
            .doc(currentUser!.uid)
            .collection('voice_commands')
            .add({
          'command': command,
          'response': response,
          'timestamp': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('Error saving voice command: $e');
    }
  }

  static Future<void> saveFaceDetectionEvent(int faceCount) async {
    try {
      if (currentUser != null) {
        await _firestore
            .collection('users')
            .doc(currentUser!.uid)
            .collection('face_detection')
            .add({
          'face_count': faceCount,
          'timestamp': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      print('Error saving face detection event: $e');
    }
  }

  // Analytics
  static Future<void> logEvent(String name, Map<String, Object>? parameters) async {
    try {
      await _analytics.logEvent(name: name, parameters: parameters);
    } catch (e) {
      print('Error logging event: $e');
    }
  }

  static Future<void> logVoiceCommand(String command) async {
    await logEvent('voice_command', {'command': command});
  }

  static Future<void> logFaceDetection(int faceCount) async {
    await logEvent('face_detection', {'face_count': faceCount});
  }

  static Future<void> logAppUsage(String feature) async {
    await logEvent('app_usage', {'feature': feature});
  }
}
