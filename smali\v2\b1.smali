.class interface abstract Lv2/b1;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract a()V
.end method

.method public abstract b(Lx2/g;)V
.end method

.method public abstract c(I)Lx2/g;
.end method

.method public abstract d()I
.end method

.method public abstract e(Ljava/lang/Iterable;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Iterable<",
            "Lw2/l;",
            ">;)",
            "Ljava/util/List<",
            "Lx2/g;",
            ">;"
        }
    .end annotation
.end method

.method public abstract f(Lx2/g;Lcom/google/protobuf/i;)V
.end method

.method public abstract g(I)Lx2/g;
.end method

.method public abstract h(Lc2/q;Ljava/util/List;Ljava/util/List;)Lx2/g;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lc2/q;",
            "Ljava/util/List<",
            "Lx2/f;",
            ">;",
            "Ljava/util/List<",
            "Lx2/f;",
            ">;)",
            "Lx2/g;"
        }
    .end annotation
.end method

.method public abstract i()Lcom/google/protobuf/i;
.end method

.method public abstract j(Lcom/google/protobuf/i;)V
.end method

.method public abstract k()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lx2/g;",
            ">;"
        }
    .end annotation
.end method

.method public abstract start()V
.end method
