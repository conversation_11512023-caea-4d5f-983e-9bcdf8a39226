import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../main.dart';

class VoiceAnimationWidget extends StatefulWidget {
  final bool isListening;

  const VoiceAnimationWidget({
    super.key,
    required this.isListening,
  });

  @override
  State<VoiceAnimationWidget> createState() => _VoiceAnimationWidgetState();
}

class _VoiceAnimationWidgetState extends State<VoiceAnimationWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    if (widget.isListening) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(VoiceAnimationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isListening != oldWidget.isListening) {
      if (widget.isListening) {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Main voice animation
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: widget.isListening ? _pulseAnimation.value : 1.0,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: widget.isListening
                          ? [
                              AppColors.primary.withOpacity(0.3),
                              AppColors.primary.withOpacity(0.1),
                              Colors.transparent,
                            ]
                          : [
                              AppColors.primary.withOpacity(0.1),
                              Colors.transparent,
                            ],
                    ),
                  ),
                  child: Center(
                    child: _buildVoiceIcon(),
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: 30),
          
          // Status text
          Text(
            widget.isListening ? 'Listening...' : 'Tap to speak',
            style: AppTextStyles.heading2.copyWith(
              color: widget.isListening ? AppColors.primary : AppColors.onBackground,
            ),
          ),
          
          const SizedBox(height: 10),
          
          // Instruction text
          Text(
            widget.isListening 
                ? 'Say your command now'
                : 'Say "Hey Robot" or tap the microphone',
            style: AppTextStyles.body1.copyWith(
              color: AppColors.onBackground.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 30),
          
          // Voice level indicator
          if (widget.isListening) _buildVoiceLevelIndicator(),
        ],
      ),
    );
  }

  Widget _buildVoiceIcon() {
    // Try to load Lottie animation, fallback to icon
    return SizedBox(
      width: 120,
      height: 120,
      child: Lottie.asset(
        'assets/lottie/speech.json',
        animate: widget.isListening,
        repeat: widget.isListening,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.isListening ? AppColors.primary : AppColors.primary.withOpacity(0.3),
            ),
            child: Icon(
              widget.isListening ? Icons.mic : Icons.mic_none,
              size: 60,
              color: AppColors.onPrimary,
            ),
          );
        },
      ),
    );
  }

  Widget _buildVoiceLevelIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(5, (index) {
        return AnimatedContainer(
          duration: Duration(milliseconds: 200 + (index * 100)),
          margin: const EdgeInsets.symmetric(horizontal: 2),
          width: 4,
          height: widget.isListening ? (20 + (index * 10)).toDouble() : 10,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(2),
          ),
        );
      }),
    );
  }
}
