.class Lv4/b$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ls4/h$b;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lv4/b;-><init>(Lv4/b$c;Ls4/h;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:Lv4/b;


# direct methods
.method constructor <init>(Lv4/b;)V
    .locals 0

    iput-object p1, p0, Lv4/b$a;->a:Lv4/b;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Ljava/lang/String;)V
    .locals 2

    iget-object v0, p0, Lv4/b$a;->a:Lv4/b;

    invoke-static {v0}, Lv4/b;->b(Lv4/b;)Lv4/b$c;

    move-result-object v0

    iget-object v1, p0, Lv4/b$a;->a:Lv4/b;

    invoke-static {v1, p1}, Lv4/b;->a(Lv4/b;Ljava/lang/String;)Landroid/view/PointerIcon;

    move-result-object p1

    invoke-interface {v0, p1}, Lv4/b$c;->setPointerIcon(Landroid/view/PointerIcon;)V

    return-void
.end method
