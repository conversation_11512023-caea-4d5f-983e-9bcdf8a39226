{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Downloads/Phone Link/output/bonic_bot_flutter/build/.cxx/Debug/2f5i6g4g/arm64-v8a", "source": "C:/Users/<USER>/AppData/Local/flutter/flutter_windows_3.32.1-stable/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}