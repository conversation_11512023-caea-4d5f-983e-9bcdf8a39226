@echo off
echo ========================================
echo    Fixing Android Issues and Running App
echo ========================================
echo.

cd bonic_bot_flutter

echo [1/4] Cleaning previous build...
flutter clean

echo [2/4] Getting dependencies...
flutter pub get

echo [3/4] Copying missing assets (if any)...
REM Copy assets if they don't exist
if not exist "assets\logo.png" (
    if exist "..\assets\flutter_assets\assets\logo.png" copy "..\assets\flutter_assets\assets\logo.png" "assets\logo.png" >nul 2>&1
)
if not exist "assets\song1.mp3" (
    if exist "..\assets\flutter_assets\assets\song1.mp3" copy "..\assets\flutter_assets\assets\song1.mp3" "assets\song1.mp3" >nul 2>&1
)
if not exist "assets\song2.mp3" (
    if exist "..\assets\flutter_assets\assets\song2.mp3" copy "..\assets\flutter_assets\assets\song2.mp3" "assets\song2.mp3" >nul 2>&1
)

echo [4/4] Running the app...
echo.
echo ========================================
echo    Starting BonicBot Flutter App
echo ========================================
echo.
echo The app should now build successfully with:
echo ✓ Android NDK 27.0.12077973
echo ✓ Minimum SDK 26
echo ✓ Firebase configuration
echo ✓ All dependencies resolved
echo.

flutter run

echo.
echo ========================================
echo    Build Complete!
echo ========================================
echo.
echo If the app is running successfully, you can now test:
echo.
echo 🎤 VOICE COMMANDS:
echo   • "Hey Robot, play music"
echo   • "Alexa, start face detection"
echo   • "Jarvis, take a photo"
echo   • "Computer, what time is it?"
echo.
echo 📱 TAP CONTROLS:
echo   • Tap microphone button to start listening
echo   • Use the tabs to switch between features
echo   • Adjust settings in the Control panel
echo.
pause
