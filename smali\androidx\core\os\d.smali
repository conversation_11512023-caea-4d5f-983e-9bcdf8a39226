.class public final Landroidx/core/os/d;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/core/os/d$a;
    }
.end annotation


# direct methods
.method public static a(Landroid/os/Handler;)Ljava/util/concurrent/Executor;
    .locals 1

    new-instance v0, Landroidx/core/os/d$a;

    invoke-direct {v0, p0}, Landroidx/core/os/d$a;-><init>(Landroid/os/Handler;)V

    return-object v0
.end method
