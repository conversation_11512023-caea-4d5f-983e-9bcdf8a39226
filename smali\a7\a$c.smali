.class final La7/a$c;
.super Ly6/f;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = La7/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x12
    name = "c"
.end annotation


# instance fields
.field private final a:La7/o;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "La7/o<",
            "*>;"
        }
    .end annotation
.end field

.field final synthetic b:La7/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "La7/a<",
            "TE;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(La7/a;La7/o;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "La7/o<",
            "*>;)V"
        }
    .end annotation

    iput-object p1, p0, La7/a$c;->b:La7/a;

    invoke-direct {p0}, Ly6/f;-><init>()V

    iput-object p2, p0, La7/a$c;->a:La7/o;

    return-void
.end method


# virtual methods
.method public a(Ljava/lang/Throwable;)V
    .locals 0

    iget-object p1, p0, La7/a$c;->a:La7/o;

    invoke-virtual {p1}, Lkotlinx/coroutines/internal/o;->v()Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, La7/a$c;->b:La7/a;

    invoke-virtual {p1}, La7/a;->t()V

    :cond_0
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ljava/lang/Throwable;

    invoke-virtual {p0, p1}, La7/a$c;->a(Ljava/lang/Throwable;)V

    sget-object p1, Lg6/t;->a:Lg6/t;

    return-object p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "RemoveReceiveOnCancel["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, La7/a$c;->a:La7/o;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x5d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
