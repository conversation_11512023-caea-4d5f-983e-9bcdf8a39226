.class public final synthetic Lv2/c4;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements La3/n;


# instance fields
.field public final synthetic a:Lv2/f4$b;


# direct methods
.method public synthetic constructor <init>(Lv2/f4$b;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lv2/c4;->a:Lv2/f4$b;

    return-void
.end method


# virtual methods
.method public final accept(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lv2/c4;->a:Lv2/f4$b;

    check-cast p1, Landroid/database/Cursor;

    invoke-static {v0, p1}, Lv2/f4;->k(Lv2/f4$b;Landroid/database/Cursor;)V

    return-void
.end method
