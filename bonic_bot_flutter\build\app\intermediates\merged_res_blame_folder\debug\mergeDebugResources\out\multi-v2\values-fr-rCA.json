{"logs": [{"outputFile": "com.example.bonic_bot.app-mergeDebugResources-48:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4641", "endColumns": "160", "endOffsets": "4797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2855,2953,3055,3154,3256,3360,3464,6899", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "2948,3050,3149,3251,3355,3459,3573,6995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,492,661,747", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "170,267,344,487,656,742,822"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5993,6170,6593,6670,7000,7169,7255", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "6058,6262,6665,6808,7164,7250,7330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f660676bf4ab7e115492941cf8444d98\\transformed\\browser-1.4.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "6063,6267,6369,6488", "endColumns": "106,101,118,104", "endOffsets": "6165,6364,6483,6588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a3d296532cc5acbfa6d00cce05e38839\\transformed\\jetified-play-services-base-18.3.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3578,3684,3864,3994,4103,4274,4407,4528,4802,4997,5109,5294,5430,5590,5769,5842,5909", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "3679,3859,3989,4098,4269,4402,4523,4636,4992,5104,5289,5425,5585,5764,5837,5904,5988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,922,1013,1105,1203,1298,1399,1492,1585,1680,1771,1862,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,80,90,91,97,94,100,92,92,94,90,90,84,109,110,102,110,107,106,158,98,85", "endOffsets": "211,318,428,515,621,751,836,917,1008,1100,1198,1293,1394,1487,1580,1675,1766,1857,1942,2052,2163,2266,2377,2485,2592,2751,2850,2936"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,922,1013,1105,1203,1298,1399,1492,1585,1680,1771,1862,1947,2057,2168,2271,2382,2490,2597,2756,6813", "endColumns": "110,106,109,86,105,129,84,80,90,91,97,94,100,92,92,94,90,90,84,109,110,102,110,107,106,158,98,85", "endOffsets": "211,318,428,515,621,751,836,917,1008,1100,1198,1293,1394,1487,1580,1675,1766,1857,1942,2052,2163,2266,2377,2485,2592,2751,2850,6894"}}]}]}