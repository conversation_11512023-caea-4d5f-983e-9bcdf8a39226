.class public interface abstract Landroidx/core/app/a$c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/app/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "c"
.end annotation


# virtual methods
.method public abstract a(Landroid/app/Activity;IILandroid/content/Intent;)Z
.end method

.method public abstract b(Landroid/app/Activity;[Ljava/lang/String;I)Z
.end method
