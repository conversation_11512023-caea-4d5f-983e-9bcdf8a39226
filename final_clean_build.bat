@echo off
echo ========================================
echo    Final Clean Build - BonicBot Simplified
echo ========================================
echo.

cd bonic_bot_flutter

echo [1/4] Deep cleaning...
flutter clean
rmdir /s /q build 2>nul
rmdir /s /q .dart_tool 2>nul

echo [2/4] Getting dependencies...
flutter pub get

echo [3/4] Copying essential assets...
if not exist "assets" mkdir assets

REM Copy basic audio files if available
if exist "..\assets\flutter_assets\assets\song1.mp3" (
    copy "..\assets\flutter_assets\assets\song1.mp3" "assets\song1.mp3" >nul 2>&1
    echo ✓ song1.mp3 copied
)
if exist "..\assets\flutter_assets\assets\song2.mp3" (
    copy "..\assets\flutter_assets\assets\song2.mp3" "assets\song2.mp3" >nul 2>&1
    echo ✓ song2.mp3 copied
)

REM Copy basic images if available
if exist "..\assets\flutter_assets\assets\logo.png" (
    copy "..\assets\flutter_assets\assets\logo.png" "assets\logo.png" >nul 2>&1
    echo ✓ logo.png copied
)

echo [4/4] Building and running...
echo.
echo ========================================
echo    Starting BonicBot (Simplified Version)
echo ========================================
echo.
echo 📱 FEATURES AVAILABLE:
echo ✓ Voice Recognition (Manual activation)
echo ✓ Text-to-Speech responses
echo ✓ Face Detection with ML Kit
echo ✓ Camera photo/video capture
echo ✓ Audio playback
echo ✓ Material Design UI
echo.
echo 🚫 REMOVED FOR SIMPLICITY:
echo ✗ Firebase authentication
echo ✗ Wake word detection
echo ✗ TensorFlow Lite models
echo ✗ Complex dependencies
echo.
echo 🎯 HOW TO USE:
echo • Tap microphone button to start listening
echo • Say commands like "play music", "stop music"
echo • Use camera tab for face detection
echo • Switch between Voice, Face, Control tabs
echo.

flutter run

echo.
echo ========================================
echo    App Running Successfully!
echo ========================================
echo.
echo 🎤 VOICE COMMANDS TO TRY:
echo   • "play music" / "stop music"
echo   • "start face detection"
echo   • "take photo"
echo   • "hello" / "how are you"
echo   • "what time is it"
echo.
echo 📱 MANUAL CONTROLS:
echo   • Microphone button: Start voice recognition
echo   • Camera tab: Face detection
echo   • Control tab: Settings and preferences
echo.
echo 🎉 Enjoy your simplified BonicBot!
echo.
pause
