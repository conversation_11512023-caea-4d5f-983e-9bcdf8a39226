.class La7/a$b;
.super La7/o;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = La7/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "La7/o<",
        "TE;>;"
    }
.end annotation


# instance fields
.field public final d:La7/a$a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "La7/a$a<",
            "TE;>;"
        }
    .end annotation
.end field

.field public final e:Ly6/l;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ly6/l<",
            "Ljava/lang/Bo<PERSON>an;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(La7/a$a;Ly6/l;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "La7/a$a<",
            "TE;>;",
            "Ly6/l<",
            "-",
            "Ljava/lang/<PERSON>;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, La7/o;-><init>()V

    iput-object p1, p0, La7/a$b;->d:La7/a$a;

    iput-object p2, p0, La7/a$b;->e:Ly6/l;

    return-void
.end method


# virtual methods
.method public B(La7/j;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "La7/j<",
            "*>;)V"
        }
    .end annotation

    iget-object v0, p1, La7/j;->d:Ljava/lang/Throwable;

    if-nez v0, :cond_0

    iget-object v0, p0, La7/a$b;->e:Ly6/l;

    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    const/4 v2, 0x2

    const/4 v3, 0x0

    invoke-static {v0, v1, v3, v2, v3}, Ly6/l$a;->a(Ly6/l;Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    goto :goto_0

    :cond_0
    iget-object v0, p0, La7/a$b;->e:Ly6/l;

    invoke-virtual {p1}, La7/j;->G()Ljava/lang/Throwable;

    move-result-object v1

    invoke-interface {v0, v1}, Ly6/l;->h(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object v0

    :goto_0
    if-eqz v0, :cond_1

    iget-object v1, p0, La7/a$b;->d:La7/a$a;

    invoke-virtual {v1, p1}, La7/a$a;->d(Ljava/lang/Object;)V

    iget-object p1, p0, La7/a$b;->e:Ly6/l;

    invoke-interface {p1, v0}, Ly6/l;->k(Ljava/lang/Object;)V

    :cond_1
    return-void
.end method

.method public C(Ljava/lang/Object;)Lq6/l;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TE;)",
            "Lq6/l<",
            "Ljava/lang/Throwable;",
            "Lg6/t;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, La7/a$b;->d:La7/a$a;

    iget-object v0, v0, La7/a$a;->a:La7/a;

    iget-object v0, v0, La7/c;->b:Lq6/l;

    if-eqz v0, :cond_0

    iget-object v1, p0, La7/a$b;->e:Ly6/l;

    invoke-interface {v1}, Lj6/d;->getContext()Lj6/g;

    move-result-object v1

    invoke-static {v0, p1, v1}, Lkotlinx/coroutines/internal/v;->a(Lq6/l;Ljava/lang/Object;Lj6/g;)Lq6/l;

    move-result-object p1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method public c(Ljava/lang/Object;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TE;)V"
        }
    .end annotation

    iget-object v0, p0, La7/a$b;->d:La7/a$a;

    invoke-virtual {v0, p1}, La7/a$a;->d(Ljava/lang/Object;)V

    iget-object p1, p0, La7/a$b;->e:Ly6/l;

    sget-object v0, Ly6/n;->a:Lkotlinx/coroutines/internal/b0;

    invoke-interface {p1, v0}, Ly6/l;->k(Ljava/lang/Object;)V

    return-void
.end method

.method public f(Ljava/lang/Object;Lkotlinx/coroutines/internal/o$b;)Lkotlinx/coroutines/internal/b0;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TE;",
            "Lkotlinx/coroutines/internal/o$b;",
            ")",
            "Lkotlinx/coroutines/internal/b0;"
        }
    .end annotation

    iget-object p2, p0, La7/a$b;->e:Ly6/l;

    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-virtual {p0, p1}, La7/a$b;->C(Ljava/lang/Object;)Lq6/l;

    move-result-object p1

    const/4 v1, 0x0

    invoke-interface {p2, v0, v1, p1}, Ly6/l;->f(Ljava/lang/Object;Ljava/lang/Object;Lq6/l;)Ljava/lang/Object;

    move-result-object p1

    if-nez p1, :cond_0

    return-object v1

    :cond_0
    sget-object p1, Ly6/n;->a:Lkotlinx/coroutines/internal/b0;

    return-object p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "ReceiveHasNext@"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {p0}, Ly6/o0;->b(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
