import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../main.dart';
import '../services/audio_service.dart';
import '../services/voice_service.dart';
import '../services/firebase_service.dart';

class ControlPanelWidget extends StatefulWidget {
  const ControlPanelWidget({super.key});

  @override
  State<ControlPanelWidget> createState() => _ControlPanelWidgetState();
}

class _ControlPanelWidgetState extends State<ControlPanelWidget> {
  double _volume = 0.5;
  double _speechRate = 0.5;
  bool _isSignedIn = false;

  @override
  void initState() {
    super.initState();
    _checkSignInStatus();
  }

  void _checkSignInStatus() {
    setState(() {
      _isSignedIn = FirebaseService.isSignedIn;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Control Animation
          Center(
            child: SizedBox(
              width: 150,
              height: 150,
              child: Lottie.asset(
                'assets/lottie/control.json',
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.settings,
                      size: 80,
                      color: AppColors.primary,
                    ),
                  );
                },
              ),
            ),
          ),
          
          const SizedBox(height: 30),
          
          // Audio Controls Section
          _buildSection(
            title: 'Audio Controls',
            icon: Icons.volume_up,
            children: [
              _buildVolumeControl(),
              const SizedBox(height: 16),
              _buildAudioButtons(),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Voice Settings Section
          _buildSection(
            title: 'Voice Settings',
            icon: Icons.record_voice_over,
            children: [
              _buildSpeechRateControl(),
              const SizedBox(height: 16),
              _buildVoiceButtons(),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Account Section
          _buildSection(
            title: 'Account',
            icon: Icons.account_circle,
            children: [
              _buildAccountControls(),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // App Info Section
          _buildSection(
            title: 'App Information',
            icon: Icons.info,
            children: [
              _buildAppInfo(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: AppTextStyles.heading2.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildVolumeControl() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Volume: ${(_volume * 100).round()}%',
          style: AppTextStyles.body1,
        ),
        Slider(
          value: _volume,
          onChanged: (value) {
            setState(() {
              _volume = value;
            });
            AudioService.setVolume(value);
          },
          activeColor: AppColors.primary,
        ),
      ],
    );
  }

  Widget _buildSpeechRateControl() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Speech Rate: ${(_speechRate * 2).toStringAsFixed(1)}x',
          style: AppTextStyles.body1,
        ),
        Slider(
          value: _speechRate,
          onChanged: (value) {
            setState(() {
              _speechRate = value;
            });
            VoiceService.setSpeechRate(value * 2);
          },
          activeColor: AppColors.primary,
        ),
      ],
    );
  }

  Widget _buildAudioButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildControlButton(
          icon: Icons.play_arrow,
          label: 'Play',
          onPressed: () => AudioService.playMusic(),
        ),
        _buildControlButton(
          icon: Icons.pause,
          label: 'Pause',
          onPressed: () => AudioService.pauseMusic(),
        ),
        _buildControlButton(
          icon: Icons.stop,
          label: 'Stop',
          onPressed: () => AudioService.stopMusic(),
        ),
      ],
    );
  }

  Widget _buildVoiceButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildControlButton(
          icon: Icons.mic,
          label: 'Test Voice',
          onPressed: () => VoiceService.speak('Voice test successful!'),
        ),
        _buildControlButton(
          icon: Icons.language,
          label: 'English',
          onPressed: () => VoiceService.setLanguage('en-US'),
        ),
      ],
    );
  }

  Widget _buildAccountControls() {
    return Column(
      children: [
        Row(
          children: [
            Icon(
              _isSignedIn ? Icons.check_circle : Icons.account_circle,
              color: _isSignedIn ? Colors.green : AppColors.onSurface.withOpacity(0.5),
            ),
            const SizedBox(width: 8),
            Text(
              _isSignedIn ? 'Signed In' : 'Not Signed In',
              style: AppTextStyles.body1,
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (!_isSignedIn)
          ElevatedButton(
            onPressed: () async {
              await FirebaseService.signInAnonymously();
              _checkSignInStatus();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.onPrimary,
            ),
            child: const Text('Sign In Anonymously'),
          )
        else
          ElevatedButton(
            onPressed: () async {
              await FirebaseService.signOut();
              _checkSignInStatus();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.onError,
            ),
            child: const Text('Sign Out'),
          ),
      ],
    );
  }

  Widget _buildAppInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoRow('Version', '1.2.0'),
        _buildInfoRow('Package', 'com.example.bonic_bot'),
        _buildInfoRow('Flutter', 'Enabled'),
        _buildInfoRow('Firebase', 'Connected'),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.body1.copyWith(
              color: AppColors.onSurface.withOpacity(0.7),
            ),
          ),
          Text(
            value,
            style: AppTextStyles.body1.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.onPrimary,
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(16),
          ),
          child: Icon(icon),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTextStyles.body2,
        ),
      ],
    );
  }
}
