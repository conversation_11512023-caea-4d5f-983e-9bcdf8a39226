.class abstract Landroidx/concurrent/futures/a$b;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/concurrent/futures/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x40a
    name = "b"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Landroidx/concurrent/futures/a$a;)V
    .locals 0

    invoke-direct {p0}, Landroidx/concurrent/futures/a$b;-><init>()V

    return-void
.end method


# virtual methods
.method abstract a(Landroidx/concurrent/futures/a;Landroidx/concurrent/futures/a$e;Landroidx/concurrent/futures/a$e;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/concurrent/futures/a<",
            "*>;",
            "Landroidx/concurrent/futures/a$e;",
            "Landroidx/concurrent/futures/a$e;",
            ")Z"
        }
    .end annotation
.end method

.method abstract b(Landroidx/concurrent/futures/a;Ljava/lang/Object;Ljava/lang/Object;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/concurrent/futures/a<",
            "*>;",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            ")Z"
        }
    .end annotation
.end method

.method abstract c(Landroidx/concurrent/futures/a;Landroidx/concurrent/futures/a$i;Landroidx/concurrent/futures/a$i;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/concurrent/futures/a<",
            "*>;",
            "Landroidx/concurrent/futures/a$i;",
            "Landroidx/concurrent/futures/a$i;",
            ")Z"
        }
    .end annotation
.end method

.method abstract d(Landroidx/concurrent/futures/a$i;Landroidx/concurrent/futures/a$i;)V
.end method

.method abstract e(Landroidx/concurrent/futures/a$i;Ljava/lang/Thread;)V
.end method
