.class public final synthetic La3/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:La3/r;

.field public final synthetic b:L<PERSON>va/lang/Runnable;


# direct methods
.method public synthetic constructor <init>(La3/r;Ljava/lang/Runnable;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La3/q;->a:La3/r;

    iput-object p2, p0, La3/q;->b:Ljava/lang/Runnable;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, La3/q;->a:La3/r;

    iget-object v1, p0, La3/q;->b:Ljava/lang/Runnable;

    invoke-static {v0, v1}, La3/r;->a(La3/r;Ljava/lang/Runnable;)V

    return-void
.end method
