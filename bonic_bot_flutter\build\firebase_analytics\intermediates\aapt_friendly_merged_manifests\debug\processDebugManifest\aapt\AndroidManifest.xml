<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="io.flutter.plugins.firebase.analytics" >

    <uses-sdk android:minSdkVersion="19" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application>
        <service android:name="com.google.firebase.components.ComponentDiscoveryService" >
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>
    </application>

</manifest>