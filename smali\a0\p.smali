.class public final synthetic La0/p;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:La0/s;

.field public final synthetic b:Z

.field public final synthetic c:Ljava/lang/String;

.field public final synthetic d:Z


# direct methods
.method public synthetic constructor <init>(La0/s;ZLjava/lang/String;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La0/p;->a:La0/s;

    iput-boolean p2, p0, La0/p;->b:Z

    iput-object p3, p0, La0/p;->c:Ljava/lang/String;

    iput-boolean p4, p0, La0/p;->d:Z

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    iget-object v0, p0, La0/p;->a:La0/s;

    iget-boolean v1, p0, La0/p;->b:Z

    iget-object v2, p0, La0/p;->c:Lja<PERSON>/lang/String;

    iget-boolean v3, p0, La0/p;->d:Z

    invoke-static {v0, v1, v2, v3}, La0/s;->l(La0/s;ZLjava/lang/String;Z)V

    return-void
.end method
