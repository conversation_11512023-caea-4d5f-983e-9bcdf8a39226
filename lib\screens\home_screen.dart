import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:camera/camera.dart';
import '../main.dart';
import '../services/voice_service.dart';
import '../services/face_detection_service.dart';
import '../services/audio_service.dart';
import '../widgets/voice_animation_widget.dart';
import '../widgets/face_detection_widget.dart';
import '../widgets/control_panel_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isListening = false;
  bool _isFaceDetectionActive = false;
  String _currentCommand = '';
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeServices();
  }

  void _initializeServices() async {
    // Initialize voice service
    VoiceService.onCommandReceived = (command) {
      setState(() {
        _currentCommand = command;
      });
      _processVoiceCommand(command);
    };
    
    // Initialize face detection
    FaceDetectionService.onFaceDetected = (faceCount) {
      // Handle face detection
      print('Faces detected: $faceCount');
    };
  }

  void _processVoiceCommand(String command) {
    // Process voice commands
    command = command.toLowerCase().trim();

    // Music controls
    if (command.contains('play music') || command.contains('play song')) {
      AudioService.playMusic();
    } else if (command.contains('stop music')) {
      AudioService.stopMusic();
    } else if (command.contains('pause music')) {
      AudioService.pauseMusic();
    } else if (command.contains('resume music')) {
      AudioService.resumeMusic();
    } else if (command.contains('next song')) {
      AudioService.playSecondTrack();
    } else if (command.contains('volume up')) {
      AudioService.increaseVolume();
      _speakResponse('Volume increased');
    } else if (command.contains('volume down')) {
      AudioService.decreaseVolume();
      _speakResponse('Volume decreased');
    } else if (command.contains('mute')) {
      AudioService.mute();
      _speakResponse('Audio muted');
    } else if (command.contains('unmute')) {
      AudioService.unmute();
      _speakResponse('Audio unmuted');
    }

    // Face detection controls
    else if (command.contains('face detection') || command.contains('start face detection') || command.contains('detect faces')) {
      _toggleFaceDetection();
    } else if (command.contains('stop face detection')) {
      if (_isFaceDetectionActive) {
        _toggleFaceDetection();
      }
    }

    // Camera controls
    else if (command.contains('take photo') || command.contains('take picture')) {
      _takePhoto();
    } else if (command.contains('record video')) {
      _startVideoRecording();
    } else if (command.contains('stop recording')) {
      _stopVideoRecording();
    }

    // System responses
    else if (command.contains('what time')) {
      final now = DateTime.now();
      _speakResponse('The time is ${now.hour}:${now.minute.toString().padLeft(2, '0')}');
    } else if (command.contains('what date')) {
      final now = DateTime.now();
      _speakResponse('Today is ${now.day}/${now.month}/${now.year}');
    }

    // Default response using VoiceService
    else {
      final response = VoiceService.getResponseForCommand(command);
      _speakResponse(response);
    }
  }

  void _speakResponse(String text) {
    // Use TTS to speak response
    VoiceService.speak(text);
  }

  void _toggleListening() {
    setState(() {
      _isListening = !_isListening;
    });
    
    if (_isListening) {
      VoiceService.startListening();
    } else {
      VoiceService.stopListening();
    }
  }

  void _toggleFaceDetection() {
    setState(() {
      _isFaceDetectionActive = !_isFaceDetectionActive;
    });

    if (_isFaceDetectionActive) {
      FaceDetectionService.startDetection();
      _speakResponse('Face detection started');
    } else {
      FaceDetectionService.stopDetection();
      _speakResponse('Face detection stopped');
    }
  }

  void _takePhoto() async {
    try {
      final cameraController = FaceDetectionService.cameraController;
      if (cameraController != null && cameraController.value.isInitialized) {
        final image = await cameraController.takePicture();
        _speakResponse('Photo captured successfully');
        print('Photo saved to: ${image.path}');
      } else {
        _speakResponse('Camera not available');
      }
    } catch (e) {
      _speakResponse('Failed to take photo');
      print('Error taking photo: $e');
    }
  }

  void _startVideoRecording() async {
    try {
      final cameraController = FaceDetectionService.cameraController;
      if (cameraController != null && cameraController.value.isInitialized) {
        await cameraController.startVideoRecording();
        _speakResponse('Video recording started');
      } else {
        _speakResponse('Camera not available');
      }
    } catch (e) {
      _speakResponse('Failed to start recording');
      print('Error starting video recording: $e');
    }
  }

  void _stopVideoRecording() async {
    try {
      final cameraController = FaceDetectionService.cameraController;
      if (cameraController != null && cameraController.value.isRecordingVideo) {
        final video = await cameraController.stopVideoRecording();
        _speakResponse('Video recording stopped');
        print('Video saved to: ${video.path}');
      } else {
        _speakResponse('No recording in progress');
      }
    } catch (e) {
      _speakResponse('Failed to stop recording');
      print('Error stopping video recording: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    VoiceService.dispose();
    FaceDetectionService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'BonicBot',
          style: AppTextStyles.heading2,
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.onPrimary,
          labelColor: AppColors.onPrimary,
          unselectedLabelColor: AppColors.onPrimary.withOpacity(0.7),
          tabs: const [
            Tab(icon: Icon(Icons.mic), text: 'Voice'),
            Tab(icon: Icon(Icons.face), text: 'Face'),
            Tab(icon: Icon(Icons.settings), text: 'Control'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildVoiceTab(),
          _buildFaceDetectionTab(),
          _buildControlTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _toggleListening,
        backgroundColor: _isListening ? AppColors.error : AppColors.primary,
        child: Icon(
          _isListening ? Icons.mic : Icons.mic_none,
          color: AppColors.onPrimary,
        ),
      ),
    );
  }

  Widget _buildVoiceTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Voice Animation
          Expanded(
            flex: 2,
            child: VoiceAnimationWidget(isListening: _isListening),
          ),
          
          // Current Command Display
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Last Command:',
                  style: AppTextStyles.body2.copyWith(
                    color: AppColors.onSurface.withOpacity(0.6),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _currentCommand.isEmpty ? 'Say "Hey Robot" to start' : _currentCommand,
                  style: AppTextStyles.body1,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Voice Control Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                icon: Icons.play_arrow,
                label: 'Play Music',
                onPressed: () => AudioService.playMusic(),
              ),
              _buildActionButton(
                icon: Icons.stop,
                label: 'Stop Music',
                onPressed: () => AudioService.stopMusic(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFaceDetectionTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Face Detection Widget
          Expanded(
            child: FaceDetectionWidget(isActive: _isFaceDetectionActive),
          ),
          
          const SizedBox(height: 20),
          
          // Face Detection Toggle
          ElevatedButton.icon(
            onPressed: _toggleFaceDetection,
            icon: Icon(_isFaceDetectionActive ? Icons.stop : Icons.face),
            label: Text(_isFaceDetectionActive ? 'Stop Detection' : 'Start Detection'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _isFaceDetectionActive ? AppColors.error : AppColors.primary,
              foregroundColor: AppColors.onPrimary,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlTab() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: ControlPanelWidget(),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }
}
