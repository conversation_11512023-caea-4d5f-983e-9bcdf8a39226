{"logs": [{"outputFile": "com.example.bonic_bot.app-mergeDebugResources-48:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,266,346,480,649,730", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "170,261,341,475,644,725,802"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5806,5976,6385,6465,6782,6951,7032", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "5871,6062,6460,6594,6946,7027,7104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,898,989,1081,1176,1270,1372,1465,1560,1655,1746,1837,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,76,90,91,94,93,101,92,94,94,90,90,80,108,99,98,108,111,110,162,95,81", "endOffsets": "215,317,425,511,618,737,816,893,984,1076,1171,1265,1367,1460,1555,1650,1741,1832,1913,2022,2122,2221,2330,2442,2553,2716,2812,2894"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,898,989,1081,1176,1270,1372,1465,1560,1655,1746,1837,1918,2027,2127,2226,2335,2447,2558,2721,6599", "endColumns": "114,101,107,85,106,118,78,76,90,91,94,93,101,92,94,94,90,90,80,108,99,98,108,111,110,162,95,81", "endOffsets": "215,317,425,511,618,737,816,893,984,1076,1171,1265,1367,1460,1555,1650,1741,1832,1913,2022,2122,2221,2330,2442,2553,2716,2812,6676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2817,2914,3016,3114,3213,3327,3432,6681", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "2909,3011,3109,3208,3322,3427,3549,6777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f660676bf4ab7e115492941cf8444d98\\transformed\\browser-1.4.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5876,6067,6166,6281", "endColumns": "99,98,114,103", "endOffsets": "5971,6161,6276,6380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4544", "endColumns": "139", "endOffsets": "4679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a3d296532cc5acbfa6d00cce05e38839\\transformed\\jetified-play-services-base-18.3.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3554,3658,3826,3948,4058,4209,4334,4445,4684,4855,4964,5139,5267,5426,5587,5656,5722", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "3653,3821,3943,4053,4204,4329,4440,4539,4850,4959,5134,5262,5421,5582,5651,5717,5801"}}]}]}