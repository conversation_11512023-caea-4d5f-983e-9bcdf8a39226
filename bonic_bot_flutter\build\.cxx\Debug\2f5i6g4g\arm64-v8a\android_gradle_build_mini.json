{"buildFiles": ["C:\\Users\\<USER>\\AppData\\Local\\flutter\\flutter_windows_3.32.1-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\Phone Link\\output\\bonic_bot_flutter\\build\\.cxx\\Debug\\2f5i6g4g\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\Phone Link\\output\\bonic_bot_flutter\\build\\.cxx\\Debug\\2f5i6g4g\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}