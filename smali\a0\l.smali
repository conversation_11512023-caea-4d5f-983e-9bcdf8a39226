.class public final synthetic La0/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:La0/s;

.field public final synthetic b:F


# direct methods
.method public synthetic constructor <init>(La0/s;F)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La0/l;->a:La0/s;

    iput p2, p0, La0/l;->b:F

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, La0/l;->a:La0/s;

    iget v1, p0, La0/l;->b:F

    invoke-static {v0, v1}, La0/s;->j(La0/s;F)V

    return-void
.end method
