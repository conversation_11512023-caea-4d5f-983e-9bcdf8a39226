.class public final synthetic La0/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:La0/s;

.field public final synthetic b:Lorg/json/JSONObject;


# direct methods
.method public synthetic constructor <init>(La0/s;Lorg/json/JSONObject;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La0/k;->a:La0/s;

    iput-object p2, p0, La0/k;->b:Lorg/json/JSONObject;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 2

    iget-object v0, p0, La0/k;->a:La0/s;

    iget-object v1, p0, La0/k;->b:Lorg/json/JSONObject;

    invoke-static {v0, v1}, La0/s;->h(La0/s;Lorg/json/JSONObject;)V

    return-void
.end method
