@echo off
echo ========================================
echo    Debug Flutter Installation Check
echo ========================================
echo.

echo Testing Flutter command...
echo.

echo Method 1: Direct flutter command
flutter --version
echo Exit code: %errorlevel%
echo.

echo Method 2: Checking PATH
echo PATH contains:
echo %PATH% | findstr /i flutter
echo.

echo Method 3: Where command
where flutter
echo Exit code: %errorlevel%
echo.

echo Method 4: Flutter doctor
flutter doctor
echo Exit code: %errorlevel%
echo.

echo Method 5: PowerShell check
powershell -Command "Get-Command flutter -ErrorAction SilentlyContinue"
echo Exit code: %errorlevel%
echo.

echo ========================================
echo    Debug Complete
echo ========================================
echo.
echo If you see Flutter version above, Flutter is installed correctly.
echo If you see errors, Flutter is not installed or not in PATH.
echo.
pause
