.class La/a$b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = La/a;->j([S)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:La/e;

.field final synthetic b:[S

.field final synthetic c:La/a;


# direct methods
.method constructor <init>(La/a;La/e;[S)V
    .locals 0

    iput-object p1, p0, La/a$b;->c:La/a;

    iput-object p2, p0, La/a$b;->a:La/e;

    iput-object p3, p0, La/a$b;->b:[S

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    iget-object v0, p0, La/a$b;->a:La/e;

    iget-object v1, p0, La/a$b;->b:[S

    invoke-interface {v0, v1}, La/e;->a([S)V

    return-void
.end method
