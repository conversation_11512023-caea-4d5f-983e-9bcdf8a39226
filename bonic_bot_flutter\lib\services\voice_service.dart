import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:picovoice_flutter/picovoice_flutter.dart';
import 'package:porcupine_flutter/porcupine_flutter.dart';

class VoiceService {
  static final SpeechToText _speechToText = SpeechToText();
  static final FlutterTts _flutterTts = FlutterTts();
  static Picovoice? _picovoice;
  static Porcupine? _porcupine;
  
  static Function(String)? onCommandReceived;
  static Function()? onWakeWordDetected;
  
  static bool _isInitialized = false;
  static bool _isListening = false;

  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Initialize Speech-to-Text
      bool available = await _speechToText.initialize(
        onStatus: (status) => print('Speech status: $status'),
        onError: (error) => print('Speech error: $error'),
      );
      
      if (!available) {
        print('Speech recognition not available');
      }
      
      // Initialize Text-to-Speech
      await _flutterTts.setLanguage('en-US');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);
      
      // Initialize Wake Word Detection (Picovoice)
      await _initializePicovoice();
      
      _isInitialized = true;
      print('Voice service initialized successfully');
    } catch (e) {
      print('Error initializing voice service: $e');
    }
  }

  static Future<void> _initializePicovoice() async {
    try {
      // Initialize Porcupine for wake word detection with multiple wake words
      final wakeWordPaths = [
        'assets/Hey-robot_en_android_v2_2_0.ppn',
        'assets/alexa.ppn',
        'assets/hey_google.ppn',
        'assets/jarvis.ppn',
        'assets/computer.ppn',
        'assets/hey_siri.ppn',
        'assets/ok_google.ppn',
        'assets/terminator.ppn',
        'assets/hey_barista.ppn',
        'assets/picovoice.ppn',
      ];

      final wakeWordNames = [
        'Hey Robot', 'Alexa', 'Hey Google', 'Jarvis',
        'Computer', 'Hey Siri', 'OK Google', 'Terminator',
        'Hey Barista', 'Picovoice'
      ];

      _porcupine = await Porcupine.fromKeywordPaths(
        wakeWordPaths,
        wakeWordCallback: (keywordIndex) {
          final detectedWord = keywordIndex < wakeWordNames.length
              ? wakeWordNames[keywordIndex]
              : 'Unknown';

          print('Wake word detected: $detectedWord (index: $keywordIndex)');
          onWakeWordDetected?.call();

          // Provide audio feedback
          speak('Yes, I\'m listening');

          // Start listening for commands
          Future.delayed(Duration(milliseconds: 500), () {
            startListening();
          });
        },
        errorCallback: (error) {
          print('Porcupine error: $error');
        },
      );

      // Start wake word detection
      await _porcupine?.start();
      print('Wake word detection started with ${wakeWordPaths.length} wake words');

    } catch (e) {
      print('Error initializing Picovoice: $e');
      print('Falling back to manual activation');
      // Fallback: use manual activation
    }
  }

  static Future<void> startListening() async {
    if (!_isInitialized || _isListening) return;
    
    try {
      _isListening = true;
      
      await _speechToText.listen(
        onResult: (result) {
          if (result.finalResult) {
            String command = result.recognizedWords;
            print('Voice command: $command');
            onCommandReceived?.call(command);
            _isListening = false;
          }
        },
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 3),
        partialResults: true,
        localeId: 'en_US',
        onSoundLevelChange: (level) {
          // Handle sound level changes for UI feedback
        },
      );
    } catch (e) {
      print('Error starting speech recognition: $e');
      _isListening = false;
    }
  }

  static Future<void> stopListening() async {
    if (!_isListening) return;
    
    try {
      await _speechToText.stop();
      _isListening = false;
    } catch (e) {
      print('Error stopping speech recognition: $e');
    }
  }

  static Future<void> speak(String text) async {
    if (!_isInitialized) return;
    
    try {
      await _flutterTts.speak(text);
    } catch (e) {
      print('Error speaking text: $e');
    }
  }

  static Future<void> setLanguage(String language) async {
    try {
      await _flutterTts.setLanguage(language);
    } catch (e) {
      print('Error setting language: $e');
    }
  }

  static Future<void> setSpeechRate(double rate) async {
    try {
      await _flutterTts.setSpeechRate(rate);
    } catch (e) {
      print('Error setting speech rate: $e');
    }
  }

  static bool get isListening => _isListening;
  static bool get isInitialized => _isInitialized;

  static Future<void> dispose() async {
    try {
      await _speechToText.stop();
      await _flutterTts.stop();
      await _porcupine?.delete();
      await _picovoice?.delete();
      _isInitialized = false;
      _isListening = false;
    } catch (e) {
      print('Error disposing voice service: $e');
    }
  }

  // Comprehensive voice commands
  static const Map<String, String> commandResponses = {
    // Greetings
    'hello': 'Hello! How can I help you today?',
    'hi': 'Hi there! What can I do for you?',
    'good morning': 'Good morning! Ready to start the day?',
    'good afternoon': 'Good afternoon! How\'s your day going?',
    'good evening': 'Good evening! How can I assist you?',
    'good night': 'Good night! Sweet dreams!',

    // About BonicBot
    'how are you': 'I\'m doing great! Thanks for asking.',
    'what is your name': 'I\'m BonicBot, your AI voice assistant.',
    'who are you': 'I\'m BonicBot, an intelligent voice assistant with face detection capabilities.',
    'what can you do': 'I can recognize voices, detect faces, play music, and respond to various commands.',

    // Music controls
    'play music': 'Playing music for you.',
    'play song': 'Starting your music.',
    'stop music': 'Music stopped.',
    'pause music': 'Music paused.',
    'resume music': 'Resuming music.',
    'next song': 'Playing next song.',
    'previous song': 'Playing previous song.',
    'volume up': 'Increasing volume.',
    'volume down': 'Decreasing volume.',
    'mute': 'Audio muted.',
    'unmute': 'Audio unmuted.',

    // Face detection
    'face detection': 'Starting face detection.',
    'start face detection': 'Face detection activated.',
    'stop face detection': 'Face detection stopped.',
    'detect faces': 'Looking for faces now.',
    'scan faces': 'Scanning for faces.',

    // System commands
    'take photo': 'Taking a photo.',
    'take picture': 'Capturing image.',
    'record video': 'Starting video recording.',
    'stop recording': 'Recording stopped.',

    // Information
    'what time is it': 'Let me check the time for you.',
    'what date is it': 'Today\'s date is available in your device.',
    'weather': 'I can\'t check weather directly, but you can ask your weather app.',

    // Responses
    'thank you': 'You\'re welcome!',
    'thanks': 'My pleasure!',
    'goodbye': 'Goodbye! Have a great day!',
    'bye': 'See you later!',
    'see you later': 'Until next time!',

    // Help and commands
    'help': 'I can help with voice commands, face detection, music playback, and more. Try saying "play music" or "start face detection".',
    'what commands': 'You can say: play music, face detection, hello, take photo, and many more!',
    'commands': 'Available commands include music control, face detection, greetings, and system functions.',

    // Fun responses
    'tell me a joke': 'Why don\'t robots ever panic? Because they have nerves of steel!',
    'sing a song': 'I\'m better at playing songs than singing them!',
    'dance': 'I\'d love to dance, but I don\'t have legs!',

    // Wake word responses
    'wake up': 'I\'m awake and ready!',
    'are you listening': 'Yes, I\'m always listening for your commands.',
    'can you hear me': 'I can hear you loud and clear!',
  };

  static String getResponseForCommand(String command) {
    command = command.toLowerCase().trim();
    
    for (String key in commandResponses.keys) {
      if (command.contains(key)) {
        return commandResponses[key]!;
      }
    }
    
    return 'I didn\'t understand that command. Try saying "help" for available commands.';
  }
}
