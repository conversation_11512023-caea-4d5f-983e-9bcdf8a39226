@echo off
echo Creating Flutter project...
flutter create --org com.example --project-name bonic_bot bonic_bot_flutter

echo Entering project directory...
cd bonic_bot_flutter

echo Copying pubspec.yaml...
if exist "..\pubspec.yaml" copy "..\pubspec.yaml" "pubspec.yaml"

echo Copying source files...
if exist "..\lib" xcopy "..\lib\*" "lib\" /E /Y

echo Copying assets...
if exist "..\assets" (
    if not exist "assets" mkdir assets
    xcopy "..\assets\*" "assets\" /E /Y
)

echo Installing dependencies...
flutter pub get

echo.
echo ========================================
echo Project created successfully!
echo ========================================
echo.
echo To run the app:
echo   flutter run
echo.
echo To open in VS Code:
echo   code .
echo.
pause
