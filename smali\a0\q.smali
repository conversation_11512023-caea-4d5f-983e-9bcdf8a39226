.class public final synthetic La0/q;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:La0/s;


# direct methods
.method public synthetic constructor <init>(La0/s;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, La0/q;->a:La0/s;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, La0/q;->a:La0/s;

    invoke-static {v0}, La0/s;->a(La0/s;)V

    return-void
.end method
