.class public Lai/picovoice/porcupine/PorcupineInvalidArgumentException;
.super Lai/picovoice/porcupine/PorcupineException;
.source "SourceFile"


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1}, Lai/picovoice/porcupine/PorcupineException;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/Throwable;)V
    .locals 0

    invoke-direct {p0, p1}, Lai/picovoice/porcupine/PorcupineException;-><init>(Ljava/lang/Throwable;)V

    return-void
.end method
