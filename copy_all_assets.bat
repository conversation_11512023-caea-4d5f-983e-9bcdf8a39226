@echo off
echo ========================================
echo    Copying All Assets to Flutter Project
echo ========================================
echo.

cd bonic_bot_flutter

REM Create assets directory structure
echo Creating assets directories...
if not exist "assets" mkdir assets
if not exist "assets\fonts" mkdir assets\fonts
if not exist "assets\lottie" mkdir assets\lottie

REM Copy images and main assets
echo Copying main assets...
if exist "..\assets\flutter_assets\assets\logo.png" copy "..\assets\flutter_assets\assets\logo.png" "assets\logo.png"
if exist "..\assets\flutter_assets\assets\splash.png" copy "..\assets\flutter_assets\assets\splash.png" "assets\splash.png"
if exist "..\assets\flutter_assets\assets\f1.jpeg" copy "..\assets\flutter_assets\assets\f1.jpeg" "assets\f1.jpeg"
if exist "..\assets\flutter_assets\assets\loading.json" copy "..\assets\flutter_assets\assets\loading.json" "assets\loading.json"

REM Copy audio files
echo Copying audio files...
if exist "..\assets\flutter_assets\assets\song1.mp3" copy "..\assets\flutter_assets\assets\song1.mp3" "assets\song1.mp3"
if exist "..\assets\flutter_assets\assets\song2.mp3" copy "..\assets\flutter_assets\assets\song2.mp3" "assets\song2.mp3"

REM Copy ML models
echo Copying ML models...
if exist "..\assets\flutter_assets\assets\mobilefacenet.tflite" copy "..\assets\flutter_assets\assets\mobilefacenet.tflite" "assets\mobilefacenet.tflite"

REM Copy Lottie animations
echo Copying Lottie animations...
if exist "..\assets\flutter_assets\assets\lottie" xcopy "..\assets\flutter_assets\assets\lottie\*" "assets\lottie\" /Y

REM Copy fonts
echo Copying fonts...
if exist "..\assets\flutter_assets\assets\fonts" xcopy "..\assets\flutter_assets\assets\fonts\*" "assets\fonts\" /Y

REM Copy wake word models from main assets
echo Copying wake word models...
if exist "..\assets\flutter_assets\assets\Hey-robot_en_android_v2_2_0.ppn" copy "..\assets\flutter_assets\assets\Hey-robot_en_android_v2_2_0.ppn" "assets\Hey-robot_en_android_v2_2_0.ppn"

REM Copy wake word models from res/raw
echo Copying additional wake word models from res/raw...
if exist "..\res\raw\alexa.ppn" copy "..\res\raw\alexa.ppn" "assets\alexa.ppn"
if exist "..\res\raw\americano.ppn" copy "..\res\raw\americano.ppn" "assets\americano.ppn"
if exist "..\res\raw\blueberry.ppn" copy "..\res\raw\blueberry.ppn" "assets\blueberry.ppn"
if exist "..\res\raw\bumblebee.ppn" copy "..\res\raw\bumblebee.ppn" "assets\bumblebee.ppn"
if exist "..\res\raw\computer.ppn" copy "..\res\raw\computer.ppn" "assets\computer.ppn"
if exist "..\res\raw\grapefruit.ppn" copy "..\res\raw\grapefruit.ppn" "assets\grapefruit.ppn"
if exist "..\res\raw\grasshopper.ppn" copy "..\res\raw\grasshopper.ppn" "assets\grasshopper.ppn"
if exist "..\res\raw\hey_barista.ppn" copy "..\res\raw\hey_barista.ppn" "assets\hey_barista.ppn"
if exist "..\res\raw\hey_google.ppn" copy "..\res\raw\hey_google.ppn" "assets\hey_google.ppn"
if exist "..\res\raw\hey_siri.ppn" copy "..\res\raw\hey_siri.ppn" "assets\hey_siri.ppn"
if exist "..\res\raw\jarvis.ppn" copy "..\res\raw\jarvis.ppn" "assets\jarvis.ppn"
if exist "..\res\raw\ok_google.ppn" copy "..\res\raw\ok_google.ppn" "assets\ok_google.ppn"
if exist "..\res\raw\pico_clock.ppn" copy "..\res\raw\pico_clock.ppn" "assets\pico_clock.ppn"
if exist "..\res\raw\picovoice.ppn" copy "..\res\raw\picovoice.ppn" "assets\picovoice.ppn"
if exist "..\res\raw\porcupine.ppn" copy "..\res\raw\porcupine.ppn" "assets\porcupine.ppn"
if exist "..\res\raw\porcupine_params.pv" copy "..\res\raw\porcupine_params.pv" "assets\porcupine_params.pv"
if exist "..\res\raw\terminator.ppn" copy "..\res\raw\terminator.ppn" "assets\terminator.ppn"

echo.
echo ========================================
echo    Assets Copied Successfully!
echo ========================================
echo.
echo Now run: flutter pub get
echo Then run: flutter run
echo.
pause
