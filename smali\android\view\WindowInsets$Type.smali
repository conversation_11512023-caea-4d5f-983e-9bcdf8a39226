.class public final synthetic Landroid/view/WindowInsets$Type;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Ljava/lang/NoClassDefFoundError;

    invoke-direct {v0}, Ljava/lang/NoClassDefFoundError;-><init>()V

    throw v0
.end method

.method public static native synthetic captionBar()I
.end method

.method public static native synthetic displayCutout()I
.end method

.method public static native synthetic ime()I
.end method

.method public static native synthetic mandatorySystemGestures()I
.end method

.method public static native synthetic navigationBars()I
.end method

.method public static native synthetic statusBars()I
.end method

.method public static native synthetic systemGestures()I
.end method

.method public static native synthetic tappableElement()I
.end method
