{"v": "5.5.6", "fr": 60, "ip": 0, "op": 300, "w": 582, "h": 533, "nm": "117. Face ID Jump", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 117, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 163, "s": [0]}, {"t": 166, "s": [100]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 0, "k": [292.5, 475.5, 0], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Anchor Point - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Anchor Point - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Anchor Point - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 13, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 14, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 15, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 163, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.932]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [9.877]}, "t": 34, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 133, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 165, "s": [50]}, {"t": 172, "s": [100]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 0, "k": [325.5, 475.5, 0], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Anchor Point - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Anchor Point - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Anchor Point - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 13, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 14, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 15, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 163, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 165, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 166, "s": [100]}, {"t": 170, "s": [0]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 0, "k": [259.5, 475.5, 0], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Anchor Point - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Anchor Point - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Anchor Point - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 13, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 14, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 15, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 163, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 7, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 25, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 156, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 157, "s": [40]}, {"t": 183, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 25, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 156, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 157, "s": [0]}, {"t": 183, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 7, "s": [292.5, 307.5, 0], "to": [-5.583, 27.917, 0], "ti": [5.583, -27.917, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 25, "s": [259, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [259, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 156, "s": [259, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 157, "s": [259, 475, 0], "to": [5.583, -27.917, 0], "ti": [-5.583, 27.917, 0]}, {"t": 183, "s": [292.5, 307.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 7, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 25, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 156, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 157, "s": [100, 100, 100]}, {"t": 183, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 2, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 17, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 164, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 165, "s": [40]}, {"t": 191, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 17, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 18, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 164, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 165, "s": [0]}, {"t": 191, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 2, "s": [319.5, 265.5, 0], "to": [-4.583, 35, 0], "ti": [4.583, -35, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 17, "s": [292, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [292, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 164, "s": [292, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 165, "s": [292, 475.5, 0], "to": [4.583, -35, 0], "ti": [-4.583, 35, 0]}, {"t": 191, "s": [319.5, 265.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 2, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 17, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 164, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 165, "s": [100, 100, 100]}, {"t": 191, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 0, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 24, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 174, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 175, "s": [40]}, {"t": 201, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 24, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 25, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 174, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 175, "s": [0]}, {"t": 201, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 0, "s": [268.5, 265.5, 0], "to": [9.5, 35, 0], "ti": [-9.5, -35, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 24, "s": [325.5, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [325.5, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 174, "s": [325.5, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 175, "s": [325.5, 475.5, 0], "to": [-9.5, -35, 0], "ti": [9.5, 35, 0]}, {"t": 201, "s": [268.5, 265.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 25, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 174, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 175, "s": [100, 100, 100]}, {"t": 201, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 5, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 22, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 182, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [40]}, {"t": 209, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 5, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 22, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 182, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [0]}, {"t": 209, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 5, "s": [319.5, 223.5, 0], "to": [-4.583, 41.917, 0], "ti": [4.583, -41.917, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 22, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 182, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 183, "s": [292, 475, 0], "to": [4.583, -41.917, 0], "ti": [-4.583, 41.917, 0]}, {"t": 209, "s": [319.5, 223.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 5, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 22, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 182, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 183, "s": [100, 100, 100]}, {"t": 209, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 16, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 34, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 172, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 173, "s": [40]}, {"t": 199, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 16, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 34, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 35, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 172, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 173, "s": [0]}, {"t": 199, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 16, "s": [361.5, 277.5, 0], "to": [-11.583, 32.917, 0], "ti": [11.583, -32.917, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 34, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 172, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 173, "s": [292, 475, 0], "to": [11.583, -32.917, 0], "ti": [-11.583, 32.917, 0]}, {"t": 199, "s": [361.5, 277.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 16, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 34, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 35, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 172, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 173, "s": [100, 100, 100]}, {"t": 199, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 20, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 36, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 168, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 169, "s": [40]}, {"t": 195, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 20, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 36, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 168, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 169, "s": [0]}, {"t": 195, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 20, "s": [370.5, 220.5, 0], "to": [-13.083, 42.417, 0], "ti": [13.083, -42.417, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 36, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 168, "s": [292, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 169, "s": [292, 475, 0], "to": [13.083, -42.417, 0], "ti": [-13.083, 42.417, 0]}, {"t": 195, "s": [370.5, 220.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 20, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 36, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 37, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 168, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 169, "s": [100, 100, 100]}, {"t": 195, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 13, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 28, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 174, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 175, "s": [40]}, {"t": 201, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 28, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 29, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 174, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 175, "s": [0]}, {"t": 201, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 13, "s": [220.5, 277.5, 0], "to": [11.75, 32.917, 0], "ti": [-11.75, -32.917, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 28, "s": [291, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [291, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 174, "s": [291, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 175, "s": [291, 475, 0], "to": [-11.75, -32.917, 0], "ti": [11.75, 32.917, 0]}, {"t": 201, "s": [220.5, 277.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 13, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 28, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 29, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 174, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 175, "s": [100, 100, 100]}, {"t": 201, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 18, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 30, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 182, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [40]}, {"t": 209, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 18, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 31, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 182, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 183, "s": [0]}, {"t": 209, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 18, "s": [214.5, 220.5, 0], "to": [18.333, 42.417, 0], "ti": [-18.333, -42.417, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 30, "s": [324.5, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [324.5, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 182, "s": [324.5, 475, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 183, "s": [324.5, 475, 0], "to": [-18.333, -42.417, 0], "ti": [18.333, 42.417, 0]}, {"t": 209, "s": [214.5, 220.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 30, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 31, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 182, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 183, "s": [100, 100, 100]}, {"t": 209, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 7, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 23, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 192, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 193, "s": [40]}, {"t": 219, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 23, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 24, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 192, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 193, "s": [0]}, {"t": 219, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 7, "s": [268.5, 223.5, 0], "to": [9.25, 42.083, 0], "ti": [-9.25, -42.083, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 23, "s": [324, 476, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [324, 476, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 192, "s": [324, 476, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 193, "s": [324, 476, 0], "to": [-9.25, -42.083, 0], "ti": [9.25, 42.083, 0]}, {"t": 219, "s": [268.5, 223.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 7, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 192, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 193, "s": [100, 100, 100]}, {"t": 219, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 2, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 25, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 176, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 177, "s": [40]}, {"t": 203, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 25, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 176, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 177, "s": [0]}, {"t": 203, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 2, "s": [319.5, 169.5, 0], "to": [-10.083, 51, 0], "ti": [10.083, -51, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 25, "s": [259, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [259, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 176, "s": [259, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 177, "s": [259, 475.5, 0], "to": [10.083, -51, 0], "ti": [-10.083, 51, 0]}, {"t": 203, "s": [319.5, 169.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 2, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 25, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 176, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 177, "s": [100, 100, 100]}, {"t": 203, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Ellipse", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 11, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 38, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 165, "s": [40]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 166, "s": [40]}, {"t": 192, "s": [40]}], "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 11, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 39, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.97], "y": [0]}, "t": 165, "s": [0]}, {"i": {"x": [0.14], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 166, "s": [0]}, {"t": 192, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.14, "y": 1}, "o": {"x": 0.97, "y": 0}, "t": 11, "s": [268.5, 169.5, 0], "to": [9.333, 51, 0], "ti": [-9.333, -51, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 38, "s": [324.5, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [324.5, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 0.14}, "o": {"x": 0.97, "y": 0.97}, "t": 165, "s": [324.5, 475.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.14, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 166, "s": [324.5, 475.5, 0], "to": [-9.333, -51, 0], "ti": [9.333, 51, 0]}, {"t": 192, "s": [268.5, 169.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 11, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 38, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 39, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.97, 0.97, 0.97], "y": [0, 0, 0]}, "t": 165, "s": [100, 100, 100]}, {"i": {"x": [0.14, 0.14, 0.14], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 166, "s": [100, 100, 100]}, {"t": 192, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Union", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [349.501, 270.001, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.429, 2.858], [-5.48, 0], [0, -1.105], [1.105, 0], [6.353, -12.706], [0.188, -3.282], [1.103, 0.063], [-0.063, 1.103]], "o": [[7.247, -14.494], [1.105, 0], [0, 1.105], [-4.52, 0], [-1.064, 2.128], [-0.063, 1.103], [-1.103, -0.063], [0.198, -3.464]], "v": [[-7.289, -34.395], [17.5, -51], [19.5, -49], [17.5, -47], [-3.711, -32.606], [-5.466, -24.386], [-7.577, -22.503], [-9.459, -24.614]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-1.1, 0.103], [-0.103, -1.1], [0, 0], [-0.054, -0.421], [-0.09, -0.581], [-0.039, -0.25], [1.092, -0.168], [0.168, 1.092], [0.028, 0.18], [0.047, 0.367], [0.107, 1.144], [0, 0]], "o": [[1.1, -0.103], [0, 0], [0.09, 0.966], [0.042, 0.328], [0.029, 0.188], [0.169, 1.092], [-1.092, 0.168], [-0.035, -0.23], [-0.099, -0.639], [-0.063, -0.488], [0, 0], [-0.103, -1.1]], "v": [[-7.686, -10.991], [-5.509, -9.186], [-5.491, -9.004], [-5.308, -7.194], [-5.124, -5.958], [-5.023, -5.305], [-6.695, -3.024], [-8.976, -4.695], [-9.071, -5.306], [-9.274, -6.678], [-9.49, -8.801], [-9.491, -8.814]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[-1.059, 0.315], [-0.314, -1.059], [-0.518, -0.924], [0, 0], [-0.127, -0.235], [-1.007, -1.007], [-0.842, -0.421], [-0.951, -0.178], [-0.851, 0], [0, -1.105], [1.105, 0], [1.094, 0.205], [0.755, 0.378], [1.177, 1.177], [0, 0], [1.612, 1.824], [0, 0], [0, 0], [0, 0], [0, 0], [0.543, 0.962], [-0.962, 0.543], [0, 0], [0, 0], [-0.049, 0.154], [1.121, 2.081], [0.109, 0.193], [0.654, 2.201]], "o": [[1.059, -0.314], [0.521, 1.754], [0, 0], [0.117, 0.208], [3.345, 6.212], [0.823, 0.823], [0.245, 0.122], [0.906, 0.17], [1.105, 0], [0, 1.105], [-1.149, 0], [-1.049, -0.197], [-1.157, -0.579], [0, 0], [-0.422, -0.422], [0, 0], [0, 0], [0, 0], [0, 0], [-0.962, 0.543], [-0.543, -0.962], [0, 0], [0, 0], [0.011, -0.169], [-1.376, -1.836], [-0.101, -0.187], [-0.548, -0.973], [-0.314, -1.059]], "v": [[-4.069, 8.083], [-1.583, 9.43], [-0.103, 12.892], [-0.103, 12.893], [0.261, 13.552], [10.414, 26.586], [13.395, 28.711], [15.244, 29.222], [18, 29.5], [20, 31.5], [18, 33.5], [14.507, 33.153], [11.606, 32.289], [7.586, 29.414], [7.573, 29.401], [4.262, 25.877], [3.496, 38.125], [3.429, 39.208], [2.483, 39.742], [-17.017, 50.742], [-19.742, 49.983], [-18.982, 47.258], [-0.428, 36.791], [0.504, 21.875], [0.595, 21.389], [-3.261, 15.448], [-3.575, 14.882], [-5.417, 10.569]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 5, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Union", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Union", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [238.49900000000002, 270.001, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.429, 2.858], [-5.48, 0], [0, -1.105], [1.105, 0], [6.353, -12.706], [0.188, -3.282], [1.103, 0.063], [-0.063, 1.103]], "o": [[7.247, -14.494], [1.105, 0], [0, 1.105], [-4.52, 0], [-1.064, 2.128], [-0.063, 1.103], [-1.103, -0.063], [0.198, -3.464]], "v": [[-7.289, -34.395], [17.5, -51], [19.5, -49], [17.5, -47], [-3.711, -32.606], [-5.466, -24.386], [-7.577, -22.503], [-9.459, -24.614]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-1.1, 0.103], [-0.103, -1.1], [0, 0], [-0.054, -0.421], [-0.09, -0.581], [-0.039, -0.25], [1.092, -0.168], [0.168, 1.092], [0.028, 0.18], [0.047, 0.367], [0.107, 1.144], [0, 0]], "o": [[1.1, -0.103], [0, 0], [0.09, 0.966], [0.042, 0.328], [0.029, 0.188], [0.169, 1.092], [-1.092, 0.168], [-0.035, -0.23], [-0.099, -0.639], [-0.063, -0.488], [0, 0], [-0.103, -1.1]], "v": [[-7.686, -10.991], [-5.509, -9.186], [-5.491, -9.004], [-5.308, -7.194], [-5.124, -5.958], [-5.023, -5.305], [-6.695, -3.024], [-8.976, -4.695], [-9.071, -5.306], [-9.274, -6.678], [-9.49, -8.801], [-9.491, -8.814]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[-1.059, 0.315], [-0.314, -1.059], [-0.518, -0.924], [0, 0], [-0.127, -0.235], [-1.007, -1.007], [-0.842, -0.421], [-0.951, -0.178], [-0.851, 0], [0, -1.105], [1.105, 0], [1.094, 0.205], [0.755, 0.378], [1.177, 1.177], [0, 0], [1.612, 1.824], [0, 0], [0, 0], [0, 0], [0, 0], [0.543, 0.962], [-0.962, 0.543], [0, 0], [0, 0], [-0.049, 0.154], [1.121, 2.081], [0.109, 0.193], [0.654, 2.201]], "o": [[1.059, -0.314], [0.521, 1.754], [0, 0], [0.117, 0.208], [3.345, 6.212], [0.823, 0.823], [0.245, 0.122], [0.906, 0.17], [1.105, 0], [0, 1.105], [-1.149, 0], [-1.049, -0.197], [-1.157, -0.579], [0, 0], [-0.422, -0.422], [0, 0], [0, 0], [0, 0], [0, 0], [-0.962, 0.543], [-0.543, -0.962], [0, 0], [0, 0], [0.011, -0.169], [-1.376, -1.836], [-0.101, -0.187], [-0.548, -0.973], [-0.314, -1.059]], "v": [[-4.069, 8.083], [-1.583, 9.43], [-0.103, 12.892], [-0.103, 12.893], [0.261, 13.552], [10.414, 26.586], [13.395, 28.711], [15.244, 29.222], [18, 29.5], [20, 31.5], [18, 33.5], [14.507, 33.153], [11.606, 32.289], [7.586, 29.414], [7.573, 29.401], [4.262, 25.877], [3.496, 38.125], [3.429, 39.208], [2.483, 39.742], [-17.017, 50.742], [-19.742, 49.983], [-18.982, 47.258], [-0.428, 36.791], [0.504, 21.875], [0.595, 21.389], [-3.261, 15.448], [-3.575, 14.882], [-5.417, 10.569]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 5, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Union", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.82], "y": [0]}, "t": 1, "s": [0]}, {"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.82], "y": [0]}, "t": 22, "s": [0]}, {"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 179, "s": [0]}, {"t": 198, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.84, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 1, "s": [453, 426, 0], "to": [6.667, 6.667, 0], "ti": [-6.667, -6.667, 0]}, {"i": {"x": 0.84, "y": 0.84}, "o": {"x": 0.82, "y": 0.82}, "t": 22, "s": [493, 466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.84, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 179, "s": [493, 466, 0], "to": [-6.667, -6.667, 0], "ti": [6.667, 6.667, 0]}, {"t": 198, "s": [453, 426, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Anchor Point - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Anchor Point - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Anchor Point - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "s": {"a": 1, "k": [{"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.82, 0.82, 0.82], "y": [0, 0, 0]}, "t": 1, "s": [100, 100, 100]}, {"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.82, 0.82, 0.82], "y": [0, 0, 0]}, "t": 22, "s": [110.00000000000001, 110.00000000000001, 100]}, {"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 179, "s": [110.00000000000001, 110.00000000000001, 100]}, {"t": 198, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 72, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 13, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 14, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 15, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[11.5, -11.5], [11.5, 11.5], [-11.5, 11.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.82], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.82], "y": [0]}, "t": 28, "s": [0]}, {"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 186, "s": [0]}, {"t": 207, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.84, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 7, "s": [132, 426, 0], "to": [-6.667, 6.667, 0], "ti": [6.667, -6.667, 0]}, {"i": {"x": 0.84, "y": 0.84}, "o": {"x": 0.82, "y": 0.82}, "t": 28, "s": [92, 466, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.84, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 186, "s": [92, 466, 0], "to": [6.667, -6.667, 0], "ti": [-6.667, 6.667, 0]}, {"t": 207, "s": [132, 426, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Anchor Point - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Anchor Point - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Anchor Point - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "s": {"a": 1, "k": [{"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.82, 0.82, 0.82], "y": [0, 0, 0]}, "t": 7, "s": [100, 100, 100]}, {"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.82, 0.82, 0.82], "y": [0, 0, 0]}, "t": 28, "s": [110.00000000000001, 110.00000000000001, 100]}, {"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 186, "s": [110.00000000000001, 110.00000000000001, 100]}, {"t": 207, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 13, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 14, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 15, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[11.5, 11.5], [-11.5, 11.5], [-11.5, -11.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.82], "y": [0]}, "t": 9, "s": [0]}, {"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 19, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 190, "s": [0]}, {"t": 210, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.84, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 9, "s": [124.1, 111.00000000000001, 0], "to": [-6.667, -6.667, 0], "ti": [6.667, 6.667, 0]}, {"i": {"x": 0.84, "y": 0.84}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [84.1, 71, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 190, "s": [84.1, 71, 0], "to": [6.667, 6.667, 0], "ti": [-6.667, -6.667, 0]}, {"t": 210, "s": [124.1, 111.00000000000001, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [-69, 0, 0], "ix": 1, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Anchor Point - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Anchor Point - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Anchor Point - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "s": {"a": 1, "k": [{"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.82, 0.82, 0.82], "y": [0, 0, 0]}, "t": 9, "s": [100, 100, 100]}, {"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 19, "s": [110.00000000000001, 110.00000000000001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 190, "s": [110.00000000000001, 110.00000000000001, 100]}, {"t": 210, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 13, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 14, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 15, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-34.5, 11.5], [-34.5, -11.5], [-11.5, -11.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Rectangle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Opacity - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Opacity - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Opacity - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 1, "k": [{"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.82], "y": [0]}, "t": 12, "s": [0]}, {"i": {"x": [0.84], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 26, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 198, "s": [0]}, {"t": 214, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.84, "y": 1}, "o": {"x": 0.82, "y": 0}, "t": 12, "s": [453, 111.00000000000001, 0], "to": [6.667, -6.667, 0], "ti": [-6.667, 6.667, 0]}, {"i": {"x": 0.84, "y": 0.84}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [493, 71, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 198, "s": [493, 71, 0], "to": [-6.667, 6.667, 0], "ti": [6.667, -6.667, 0]}, {"t": 214, "s": [453, 111.00000000000001, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Anchor Point - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Anchor Point - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Anchor Point - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "s": {"a": 1, "k": [{"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.82, 0.82, 0.82], "y": [0, 0, 0]}, "t": 12, "s": [100, 100, 100]}, {"i": {"x": [0.84, 0.84, 0.84], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [110.00000000000001, 110.00000000000001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 198, "s": [110.00000000000001, 110.00000000000001, 100]}, {"t": 214, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Scale - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Scale - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Scale - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Scale - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Scale - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Opacity - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Bounce", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Anchor Point - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 13, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 14, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 15, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-11.5, -11.5], [11.5, -11.5], [11.5, 11.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.25098039215686274, 0.5058823529411764, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": [{"tm": 217, "cm": "1", "dr": 0}, {"tm": 281, "cm": "2", "dr": 0}]}