@echo off
echo ========================================
echo    Clean Build and Run BonicBot
echo ========================================
echo.

cd bonic_bot_flutter

echo [1/5] Cleaning previous build...
flutter clean

echo [2/5] Getting dependencies...
flutter pub get

echo [3/5] Copying essential assets...
REM Copy basic assets if they don't exist
if not exist "assets" mkdir assets

REM Copy from decompiled APK if available
if exist "..\assets\flutter_assets\assets\song1.mp3" (
    if not exist "assets\song1.mp3" copy "..\assets\flutter_assets\assets\song1.mp3" "assets\song1.mp3" >nul 2>&1
)
if exist "..\assets\flutter_assets\assets\song2.mp3" (
    if not exist "assets\song2.mp3" copy "..\assets\flutter_assets\assets\song2.mp3" "assets\song2.mp3" >nul 2>&1
)

echo [4/5] Checking Android configuration...
echo ✓ NDK Version: 27.0.12077973
echo ✓ Min SDK: 26
echo ✓ No Firebase dependencies
echo ✓ All permissions configured

echo [5/5] Running the app...
echo.
echo ========================================
echo    Starting BonicBot (No Firebase)
echo ========================================
echo.
echo The app will run with these features:
echo ✓ Voice recognition and TTS
echo ✓ Camera and face detection
echo ✓ Audio playback
echo ✓ Wake word detection (Porcupine)
echo ✓ Material Design UI
echo.
echo Note: Firebase features are disabled for simplicity
echo.

flutter run

echo.
echo ========================================
echo    App Running Successfully!
echo ========================================
echo.
echo 🎤 TEST VOICE COMMANDS:
echo   • Tap microphone button to start listening
echo   • Say "Hey Robot, play music"
echo   • Say "Computer, start face detection"
echo   • Say "Jarvis, take a photo"
echo.
echo 📱 USE MANUAL CONTROLS:
echo   • Switch between Voice, Face, and Control tabs
echo   • Adjust settings in Control panel
echo   • Test camera and audio features
echo.
pause
